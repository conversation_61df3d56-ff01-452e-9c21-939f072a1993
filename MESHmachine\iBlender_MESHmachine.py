# -*- coding: UTF-8 -*-
import bpy


def _z(phrase):
    language = bpy.context.preferences.view.language

    if language == "zh_HANS" or language == "zh_CN":
        language = "zh_CN"
    elif language == "zh_HANT" or language == "zh_TW":
        language = "zh_TW"
    else:
        language = "en_US"

    try:
        return ztranslate[language][phrase]
    except KeyError:
        # if phrase and len(phrase) > 1:
        #     print(("""%r, #%r""") % (phrase, language))
        return phrase
    except:
        print("Bug!!!@iBlender®")
        return phrase


ztranslate = {
    "en_US": {
        "Language": "Language",
        "Buy More": "Buy More",
        "Chat Room": "Chat Room",
        "Language Description": "Language",
        "Watch online video tutorials": "Watch online video tutorials",
        "Translation is in progress. Whole text isn't translated": "Translation is in progress. Whole text isn't translated.",  #
        "Apply Stashes Mods": "Apply Stashes Mods",
        "FACE": "FACE",
        "Apply All Mods Selected Objects": "Apply All Mods Selected Objects",
        "LOOP": "LOOP",
        "Negative X": "Negative X",
        "Positive Z": "Positive Z",
        "Positive X": "Positive X",
        "Apply Stash Mods": "Apply Stash Mods",
        "No Boolean Mods found on Selected Objects": "No Boolean Mods found on Selected Objects",
        "FUSE": "FUSE",
        "LoopTools activation failed!": "LoopTools activation failed!",
        "This keymap should be the same as your native Blender Loop Select Keymapping": "This keymap should be the same as your native Blender Loop Select Keymapping",
        "If you don't use ALT + LMB for loop selecting, remap this accordingly!": "If you don't use ALT + LMB for loop selecting, remap this accordingly!",
        "Mirror Vertex Groups": "Mirror Vertex Groups",
        "Label": "Label",
        "Object Mode": "Object Mode",
        "Redundant Threshold Angle": "Redundant Threshold Angle",
        "Always Loop Select": "Always Loop Select",
        "Edit Mode": "编辑模式",
        "Requires library reload or Blender restart": "Requires library reload or Blender restart",
        "Show Plug Count next to Library name": "Show Plug Count next to Library name",
        "Show Plugs 'In Front' when bringin them into the scene": "Show Plugs 'In Front' when bringin them into the scene",
        "Name of Matcap used for Surface Check.": "Name of Matcap used for Surface Check.",
        "Print Addon Registration Output in System Console": "Print Addon Registration Output in System Console",
        "Addon": "Addon",
        "Show in Blender's Edit Mesh Context Menu": "Show in Blender's Edit Mesh Context Menu",
        "Leave Empty, to disable": "Leave Empty, to disable",
        "Show Mesh Split Tool": "Show Mesh Split Tool",
        "Show in Blender's Object Context Menu": "Show in Blender's Object Context Menu",
        "Follow Muuse": "Follow Muuse",
        "    • Force Deform: %s ": "    • Force Deform: %s ",
        "  • Force Deform: %s ": "  • Force Deform: %s ",
        "  • Type: %s": "  • Type: %s",
        "  • Use Deformer: %s": "  • Use Deformer: %s",
        "(B) Unbevel": "(B) Unbevel",
        "(C) Unchamfer": "(C) Unchamfer",
        "(D) Unfuse": "(D) Unfuse",
        "(E) Flatten": "(E) Flatten",
        "(R) Refuse": "(R) Refuse",
        "(W) Change Width": "(W) Change Width",
        "(X) Delete Plug": "(X) Delete Plug",
        "(X) Delete Plugs": "(X) Delete Plugs",
        "(X) Delete": "(X) Delete",
        "(X) Unf*ck": "(X) Unf*ck",
        "(Y) Delete Plug": "(Y) Delete Plug",
        "(Y) Delete Plugs": "(Y) Delete Plugs",
        "(Y) Split": "(Y) Split",
        "+ to -": "+ to -",
        "- to +": "- to +",
        "3D Cursor": "3D Cursor",
        "A new version is available!": "A new version is available!",
        "ALT scroll  UP/DOWN": "ALT scroll  UP/DOWN",
        "ALT scroll UP/DOWN": "ALT 滚轮 上/下",
        "About": "About",
        "Actions": "Actions",
        "Add Boolean Modifier": "Add Boolean Modifier",
        "Add Boolean": "Add Boolean",
        "Add Mode": "Add Mode",
        "Add Plug to Library": "Add Plug to Library",
        "Add a new empty library": "Add a new empty library",
        "Add selected Plug to Plug Library": "Add selected Plug to Plug Library",
        "Advanced Mode": "Advanced Mode",
        "Advanced": "Advanced",
        "Align Mode": "Align Mode",
        "Align non-cyclic edge loop along bezier": "Align non-cyclic edge loop along bezier",
        "All": "All",
        "Along Edge": "Along Edge",
        "Along Normal": "Along Normal",
        "Alpha": "Alpha",
        "Also Clear Vertex Groups": "Also Clear Vertex Groups",
        "Amount": "Amount",
        "Angle Threshold Presets": "Angle Threshold Presets",
        "Angle Threshold": "Angle Threshold",
        "Angle": "Angle",
        "Appereance": "Appereance",
        "Apply All Modifiers": "Apply All Modifiers",
        "Apply Booleans": "Apply Booleans",
        "Apply Data Transfers": "Apply Data Transfers",
        "Apply Mod": "Apply Mod",
        "Apply Normal Transfer": "Apply Normal Transfer",
        "Apply Shrink Wrap": "Apply Shrink Wrap",
        "Apply all Boolean Modifiers, and stash the Cutters": "Apply all Boolean Modifiers, and stash the Cutters",
        "Are you sure? This cannot be undone!": "Are you sure? This cannot be undone!",
        "Asset Loaders": "Asset Loaders",
        "Auto-Smooth": "Auto-Smooth",
        "Auto-X-Ray the plug and its subsets, when inserting Plug into scene": "Auto-X-Ray the plug and its subsets, when inserting Plug into scene",
        "Average Tension": "Average Tension",
        "Axis": "Axis",
        "BWeight": "BWeight",
        "Basic": "Basic",
        "Best fit": "Best fit",
        "Blender Addons": "Blender Addons",
        "Blender Market": "Blender Market",
        "Boolean Cleanup": "Boolean Cleanup",
        "Bridge": "Bridge",
        "CTRL scroll UP/DOWN": "CTRL scroll UP/DOWN",
        "CURVEmachine": "CURVEmachine",
        "Cancel": "Cancel",
        "Cap Holes": "Cap Holes",
        "Cap": "Cap",
        "Careful, values above 4 are increasingly slow": "Careful, values above 4 are increasingly slow",
        "Chamfer cyclic selections resulting from Boolean Operations": "Chamfer cyclic selections resulting from Boolean Operations",
        "Chamfer": "Chamfer",
        "Change Width": "Change Width",
        "Change the width of Chamfers(flat Bevels)": "Change the width of Chamfers(flat Bevels)",
        "Change this, so Plugs created by you, are tagged with your info!": "Change this, so Plugs created by you, are tagged with your info!",
        "Circle": "Circle",
        "Clear Center Sharps": "Clear Center Sharps",
        "Clear Existing Normals": "Clear Existing Normals",
        "Clear Loop": "Clear Loop",
        "Clear Normals": "Clear Normals",
        "Clear Plug Props": "Clear Plug Props",
        "Clear Plug properties": "Clear Plug properties",
        "Clear library prefs, resets them into their original state.\nNo plugs will be lost!\nSave prefs and restart Blender to complete the process": "Clear library prefs, resets them into their original state.\nNo plugs will be lost!\nSave prefs and restart Blender to complete the process",
        "Clear": "Clear",
        "Color": "Color",
        "Conform selection to Stash surface": "Conform selection to Stash surface",
        "Conform": "Conform",
        "Contain": "Contain",
        "Convert Mirrod Modifiers into real geometry with proper origins and properly mirrored custom normals": "Convert Mirrod Modifiers into real geometry with proper origins and properly mirrored custom normals",
        "Convert a triangular Bevel Corner to Quad Corner": "Convert a triangular Bevel Corner to Quad Corner",
        "Corner": "Corner",
        "Couldn't find 2 quads on either side of the selection!": "Couldn't find 2 quads on either side of the selection!",
        "Countdown (s)": "Countdown (s)",
        "Create Plug from mesh object(s)": "Create Plug from mesh object(s)",
        "Create Plug": "Create Plug",
        "Create RealMirror Collections": "Create RealMirror Collections",
        "Create Stash": "Create Stash",
        "Create Vertex Group": "Create Vertex Group",
        "Create Vertex Groups": "Create Vertex Groups",
        "Create Wedge from Edge Selection": "Create Wedge from Edge Selection",
        "Create a surface confirming polygon patch by drawing 4 corner points.": "Create a surface confirming polygon patch by drawing 4 corner points.",
        "Create rounded Bevels from Chamfers": "Create rounded Bevels from Chamfers",
        "Creator: %s": "Creator: %s",
        "Cubic": "Cubic",
        "Custom Normal Mirror Method": "Custom Normal Mirror Method",
        "Custom Normal Pairing Method": "Custom Normal Pairing Method",
        "Custom Radius": "Custom Radius",
        "Custom": "Custom",
        "Custom": "Custom",
        "Cyclic selections are not supported, aborting": "Cyclic selections are not supported, aborting",
        "Cyclic": "Cyclic",
        "DECALmachine": "DECALmachine",
        "DEFORMER": "DEFORMER",
        "Debug Whatever": "Debug Whatever",
        "Debug": "Debug",
        "Define Wedge Area": "Define Wedge Area",
        "Define Wedge Depth": "Define Wedge Depth",
        "Deform Plug": "Deform Plug",
        "Deform Subsets": "Deform Subsets",
        "Deformation": "Deformation",
        "Deformer Precision": "Deformer Precision",
        "Deformer": "Deformer",
        "Deformer: %s": "Deformer: %s",
        "Delete All": "Delete All",
        "Delete Handle, Plug and all Support Objects": "Delete Handle, Plug and all Support Objects",
        "Delete": "Delete",
        "Deleting All": "Deleting All",
        "Deleting Marked": "Deleting Marked",
        "Delta Matrix": "Delta Matrix",
        "Depth Amount": "Depth Amount",
        "Difference": "Difference",
        "Direction": "Direction",
        "Display": "Display",
        "Dissolve Angle": "Dissolve Angle",
        "Dissolve": "Dissolve",
        "Dissolve": "Dissolve",
        "Docs": "Docs",
        "Documentation": "Documentation",
        "Documention": "Documention",
        "Down": "Down",
        "Draw Active Stash in 3D View": "Draw Active Stash in 3D View",
        "Draw Active Stash in X-Ray": "Draw Active Stash in X-Ray",
        "Draw Debug": "Draw Debug",
        "Draw Properties": "Draw Properties",
        "Draw Timer": "Draw Timer",
        "Duplicate Boolean Objects with their Cutters\nALT: Instance the Object and Cutter Meshes": "Duplicate Boolean Objects with their Cutters\nALT: Instance the Object and Cutter Meshes",
        "Duplicate Booleans": "Duplicate Booleans",
        "Edge Index": "Edge Index",
        "Edge": "Edge",
        "Edge": "Edge",
        "Edit Fillets by using Unfuse + Fuse in sequence": "Edit Fillets by using Unfuse + Fuse in sequence",
        "Edit": "Edit",
        "Editing %s": "Editing %s",
        "Embed Plug into mesh surface": "Embed Plug into mesh surface",
        "Empties found, but no ARRAY or HOOK modifiers present!": "Empties found, but no ARRAY or HOOK modifiers present!",
        "Empty found, but no ARRAY or HOOK modifiers present!": "Empty found, but no ARRAY or HOOK modifiers present!",
        "Empty: %s": "Empty: %s",
        "Exact": "Exact",
        "Examples": "Examples",
        "Experimental Features": "Experimental Features",
        "Experimental": "Experimental",
        "Extend": "Extend",
        "Extra": "Extra",
        "FAQ": "FAQ",
        "FILLET or EDGE": "FILLET or EDGE",
        "Face Method": "Face Method",
        "Face Mode": "Face Mode",
        "Face mode": "Face mode",
        "Face": "Face",
        "Factor": "Factor",
        "Fade": "Fade",
        "Fading wire frames (experimental)": "Fading wire frames (experimental)",
        "Failed to rename library": "Failed to rename library",
        "False": "False",
        "Fast": "Fast",
        "Fillet or Edge": "Fillet or Edge",
        "Fillet": "Fillet",
        "Finish and select Cutters": "Finish and select Cutters",
        "Finish": "Finish",
        "Finish": "Finish",
        "Fit inside": "Fit inside",
        "Fix Center Method": "Fix Center Method",
        "Fix Center Seam": "Fix Center Seam",
        "Fix Midpoint": "Fix Midpoint",
        "Flatten Along": "Flatten Along",
        "Flatten Polygon(s) along Edges or Normal": "Flatten Polygon(s) along Edges or Normal",
        "Flatten uneven shading on (mostly) ngons": "Flatten uneven shading on (mostly) ngons",
        "Flatten": "Flatten",
        "Flick Distance": "Flick Distance",
        "Flick": "Flick",
        "Flip Red to Green": "Flip Red to Green",
        "Flip Wedge Direction": "Flip Wedge Direction",
        "Flip": "Flip",
        "Flip": "Flip",
        "Flipped Normals": "Flipped Normals",
        "Flipped": "Flipped",
        "Follow Mouse": "Follow Mouse",
        "Force Projected Loop": "Force Projected Loop",
        "Force Projected Loops": "Force Projected Loops",
        "Force Subset Deform": "Force Subset Deform",
        "Fuse": "Fuse",
        "General": "General",
        "Generate Log Files and Instructions for a Support Request.": "Generate Log Files and Instructions for a Support Request.",
        "Generate new UUID": "Generate new UUID",
        "Get Loops or Handles": "Get Loops or Handles",
        "Get More Plugs": "Get More Plugs",
        "HOOK or ARRAY": "HOOK or ARRAY",
        "HUD Color": "HUD Color",
        "HUD Font Color": "HUD Font Color",
        "HUD Scale": "HUD Scale",
        "HUD": "HUD",
        "HUD": "HUD",
        "Handle contains N-Gons!": "Handle contains N-Gons!",
        "Handle polygons are flipped!": "Handle polygons are flipped!",
        "Handle": "Handle",
        "Handle: %s": "Handle: %s",
        "Handles": "Handles",
        "Has Fillet": "Has Fillet",
        "Have you already removed it manualy, while Blender was running?": "Have you already removed it manualy, while Blender was running ?",
        "Help": "Help",
        "Hide Deformer and Occluder and Others": "Hide Deformer and Occluder and Others",
        "Illegal Selection": "Illegal Selection",
        "Index": "Index",
        "Influence": "Influence",
        "Info": "Info",
        "Initial Run": "Initial Run",
        "Initialize": "Initialize",
        "Input": "Input",
        "Insert Plug": "Insert Plug",
        "Insert": "Insert",
        "Instance": "Instance",
        "Integration": "Integration",
        "Interpolation Falloff (Surface Deform)": "Interpolation Falloff (Surface Deform)",
        "Interpolation": "Interpolation",
        "Interpolation": "Interpolation",
        "Intersect": "Intersect",
        "Invalid Plug": "Invalid Plug",
        "Invalid Selection": "Invalid Selection",
        "Iterations": "Iterations",
        "Keymaps": "Keymaps",
        "LMB to place, F1 to undo, F2 to redo": "LMB to place, F1 to undo, F2 to redo",
        "LOCKED": "LOCKED",
        "LSelect": "LSelect",
        "Leave empty, to disable": "Leave empty, to disable",
        "Length": "Length",
        "Libraries": "Libraries",
        "Library path could not be found, reload libraries or restart Blender.": "Library path could not be found, reload libraries or restart Blender.",
        "Library": "Library",
        "Limit by Sharps": "Limit by Sharps",
        "Limit to Selection": "Limit to Selection",
        "Linear": "Linear",
        "Linked": "Linked",
        "Local": "Local",
        "Location": "Location",
        "Lock X": "Lock X",
        "Lock Y": "Lock Y",
        "Lock Z": "Lock Z",
        "Loop Select": "Loop Select",
        "Loop Slide ": "Loop Slide",
        "Loop Slide": "Loop Slide",
        "Loop Slide": "Loop Slide",
        "Loop Slide": "Loop Slide",
        "Loop edges don't intersect.": "Loop edges don't intersect.",
        "Loop": "Loop",
        "LoopTools' Circle as a modal": "LoopTools' Circle as a modal",
        "LoopTools's Relax as a modal": "LoopTools's Relax as a modal",
        "Loops": "Loops",
        "MACHIN3": "MACHIN3",
        "MACHIN3: Add Plug Library": "MACHIN3: Add Plug Library",
        "MACHIN3: Add Plug To Library": "MACHIN3: Add Plug To Library",
        "MACHIN3: Boolean Apply": "MACHIN3: Boolean Apply",
        "MACHIN3: Boolean Cleanup": "MACHIN3: Boolean Cleanup",
        "MACHIN3: Boolean Duplicate": "MACHIN3: Boolean Duplicate",
        "MACHIN3: Boolean": "MACHIN3: Boolean",
        "MACHIN3: Boolean": "MACHIN3: Boolean",
        "MACHIN3: Call MESHmachine Menu": "MACHIN3: Call MESHmachine Menu",
        "MACHIN3: Chamfer": "MACHIN3: Chamfer",
        "MACHIN3: Change Width": "MACHIN3: Change Width",
        "MACHIN3: Clear Plug Libraries": "MACHIN3: Clear Plug Libraries",
        "MACHIN3: Clear Plug Properties": "MACHIN3: Clear Plug Properties",
        "MACHIN3: Conform": "MACHIN3: Conform",
        "MACHIN3: Create Plug": "MACHIN3: Create Plug",
        "MACHIN3: Create Stash": "MACHIN3: Create Stash",
        "MACHIN3: Debug HUD": "MACHIN3: Debug HUD",
        "MACHIN3: Debug MESHmachine": "MACHIN3: Debug MESHmachine",
        "MACHIN3: Debug Whatever": "MACHIN3: Debug Whatever",
        "MACHIN3: Delete Plug": "MACHIN3: Delete Plug",
        "MACHIN3: Draw Debug": "MACHIN3: Draw Debug",
        "MACHIN3: Draw Plug": "MACHIN3: Draw Plug",
        "MACHIN3: Draw RealMirror": "MACHIN3: Draw RealMirror",
        "MACHIN3: Draw Stash": "MACHIN3: Draw Stash",
        "MACHIN3: Draw Symmetrize": "MACHIN3: Draw Symmetrize",
        "MACHIN3: Draw Transferred Stashes": "MACHIN3: Draw Transferred Stashes",
        "MACHIN3: Flatten": "MACHIN3: Flatten",
        "MACHIN3: Fuse": "MACHIN3: Fuse",
        "MACHIN3: Get Angle": "MACHIN3: Get Angle",
        "MACHIN3: Get Faces Linked to Verts": "MACHIN3: Get Faces Linked to Verts",
        "MACHIN3: Get Length": "MACHIN3: Get Length",
        "MACHIN3: Get MESHmachine Support": "MACHIN3: Get MESHmachine Support",
        "MACHIN3: Get Sides": "MACHIN3: Get Sides",
        "MACHIN3: Loop Select": "MACHIN3: Loop Select",
        "MACHIN3: LoopTools Circle": "MACHIN3: LoopTools Circle",
        "MACHIN3: LoopTools Relax": "MACHIN3: LoopTools Relax",
        "MACHIN3: Loops or Handles": "MACHIN3: Loops or Handles",
        "MACHIN3: Make Unique": "MACHIN3: Make Unique",
        "MACHIN3: Mark Loop": "MACHIN3: Mark Loop",
        "MACHIN3: Move Plug Library": "MACHIN3: Move Plug Library",
        "MACHIN3: Normal Clear": "MACHIN3: Normal Clear",
        "MACHIN3: Normal Flatten": "MACHIN3: Normal Flatten",
        "MACHIN3: Normal Straighten": "MACHIN3: Normal Straighten",
        "MACHIN3: Normal Transfer": "MACHIN3: Normal Transfer",
        "MACHIN3: Offset Cut": "MACHIN3: Offset Cut",
        "MACHIN3: Offset": "MACHIN3: Offset",
        "MACHIN3: Offset": "MACHIN3: Offset",
        "MACHIN3: Open Plug Library": "MACHIN3: Open Plug Library",
        "MACHIN3: Plug": "MACHIN3: Plug",
        "MACHIN3: Quad Corner": "MACHIN3: Quad Corner",
        "MACHIN3: Quick Patch": "MACHIN3: Quick Patch",
        "MACHIN3: Real Mirror": "MACHIN3: Real Mirror",
        "MACHIN3: Refuse": "MACHIN3: Refuse",
        "MACHIN3: Reload Plug Libraries": "MACHIN3: Reload Plug Libraries",
        "MACHIN3: Remove Stash": "MACHIN3: Remove Stash",
        "MACHIN3: Select": "MACHIN3: Select",
        "MACHIN3: Set Plug Props": "MACHIN3: Set Plug Props",
        "MACHIN3: Sharp Select": "MACHIN3: Sharp Select",
        "MACHIN3: Swap Stash": "MACHIN3: Swap Stash",
        "MACHIN3: Sweep Stashes": "MACHIN3: Sweep Stashes",
        "MACHIN3: Symmetrize": "MACHIN3: Symmetrize",
        "MACHIN3: Transfer Stashes": "MACHIN3: Transfer Stashes",
        "MACHIN3: Turn Corner": "MACHIN3: Turn Corner",
        "MACHIN3: Unbevel": "MACHIN3: Unbevel",
        "MACHIN3: Unchamfer": "MACHIN3: Unchamfer",
        "MACHIN3: Unf*ck": "MACHIN3: Unf*ck",
        "MACHIN3: Unfuse": "MACHIN3: Unfuse",
        "MACHIN3: Validate Plug": "MACHIN3: Validate Plug",
        "MACHIN3: Vertex Group Select": "MACHIN3: Vertex Group Select",
        "MACHIN3: Vertex Info": "MACHIN3: Vertex Info",
        "MACHIN3: View Orphan Stashes": "MACHIN3: View Orphan Stashes",
        "MACHIN3: View Stashes": "MACHIN3: View Stashes",
        "MACHIN3: Wedge": "MACHIN3: Wedge",
        "MACHIN3_MT_%s": "MACHIN3_MT_%s",
        "MACHIN3tools": "MACHIN3tools",
        "MESHmachine %s": "MESHmachine %s",
        "MESHmachine Documentation": "MESHmachine Documentation",
        "MESHmachine Help": "MESHmachine Help",
        "MESHmachine": "MESHmachine",
        "MOVE LEFT/RIGHT, toggle W, reset ALT + W": "MOVE LEFT/RIGHT, toggle W, reset ALT + W",
        "Make Instanced Mesh Objects Unique incl. any Instanced Boolean Operators": "Make Instanced Mesh Objects Unique incl. any Instanced Boolean Operators",
        "Make Unique": "Make Unique",
        "Make sure they aren't just ARRAY caps, that need to have their props cleared!": "Make sure they aren't just ARRAY caps, that need to have their props cleared!",
        "Make sure to select a single, manifold edge, with at least one end leading to exactly 2 other edges!": "Make sure to select a single, manifold edge, with at least one end leading to exactly 2 other edges!",
        "Mapping": "Mapping",
        "Mark Loop": "Mark Loop",
        "Mark/Unmark edges for preferential treatement by Fuse/Refuse": "Mark/Unmark edges for preferential treatement by Fuse/Refuse",
        "MatCap Switch": "MatCap Switch",
        "Matcap used for Surface Check.": "Matcap used for Surface Check.",
        "Menu": "Menu",
        "Merge Perimeter": "Merge Perimeter",
        "Merge verts on cyclic selections resulting from Boolean operations": "Merge verts on cyclic selections resulting from Boolean operations",
        "Merge": "Merge",
        "Mesh Deform": "Mesh Deform",
        "Method": "Method",
        "Min Angle": "Min Angle",
        "Mirror Custom Normals": "Mirror Custom Normals",
        "Modal": "Modal",
        "Mode": "Mode",
        "Mode: Symmstrize": "Mode: Symmstrize",
        "Modifier: %s": "Modifier: %s",
        "Mouse Pointer": "Mouse Pointer",
        "Mouse Position for Plug Insertion": "Mouse Position for Plug Insertion",
        "Move library up or down.\nThis controls the position in the MESHmachine Plug Libraries submenu.\nSave prefs to remember": "Move library up or down.\nThis controls the position in the MESHmachine Plug Libraries submenu.\nSave prefs to remember",
        "Multiple Creators": "Multiple Creators",
        "Multiple Deformers found!": "Multiple Deformers found!",
        "Multiple Empties": "Multiple Empties",
        "Multiple Empties:": "Multiple Empties:",
        "Multiple Handles found!": "Multiple Handles found!",
        "Multiple Modifiers:": "Multiple Modifiers:",
        "Multiple Occluders found!": "Multiple Occluders found!",
        "Multiple Others:": "Multiple Others:",
        "Multiple Plug Meshes found!": "Multiple Plug Meshes found!",
        "Multiple Subsets": "Multiple Subsets",
        "Multiple Subsets:": "Multiple Subsets:",
        "Multiple UUIDs": "Multiple UUIDs",
        "My other Blender Addons": "My other Blender Addons",
        "Name (optiona)": "Name (optiona)",
        "Name (optional)": "Name (optional)",
        "Nearest Corner and Best Matching Face Normal": "Nearest Corner and Best Matching Face Normal",
        "Nearest Corner and Best Matching Normal": "Nearest Corner and Best Matching Normal",
        "Nearest Face Interpolated": "Nearest Face Interpolated",
        "Nearest Vertex": "Nearest Vertex",
        "New Library Name": "New Library Name",
        "New Name": "New Name",
        "New Name:": "New Name:",
        "New": "New",
        "No Handle found!": "No Handle found!",
        "No Plug Mesh found!": "No Plug Mesh found!",
        "No active object in selection.": "No active object in selection.",
        "No active object!": "No active object!",
        "No new name chosen.": "No new name chosen.",
        "Non-Manifold Geometry": "Non-Manifold Geometry",
        "Non-manifold edges are part of the selection. Failed to determine sides of the selection.": "Non-manifold edges are part of the selection. Failed to determine sides of the selection.",
        "None": "None",
        "None": "None",
        "Normal Flatten": "Normal Flatten",
        "Normal Transfer Matcap": "Normal Transfer Matcap",
        "Normal Transfer": "Normal Transfer",
        "Normals": "Normals",
        "Not covered by Product Support!": "Not covered by Product Support!",
        "Objet Mode": "Objet Mode",
        "Occluder": "Occluder",
        "Occluder: %s": "Occluder: %s",
        "Offset Cut": "Offset Cut",
        "Offset Patch": "Offset Patch",
        "Offset cyclic edgeloops resulting from Boolean Operations to create a perimeter loop": "Offset cyclic edgeloops resulting from Boolean Operations to create a perimeter loop",
        "Offset": "Offset",
        "Offset": "Offset",
        "Old Name:": "Old Name:",
        "Online": "Online",
        "Only single-island cyclic or non-cyclic edge loop selections are supproted.": "Only single-island cyclic or non-cyclic edge loop selections are supproted.",
        "Open selected library in file browser": "Open selected library in file browser",
        "Optimize": "Optimize",
        "Orphan Matrix": "Orphan Matrix",
        "Other object found, but no ARRAY modifiers present! What is it?": "Other object found, but no ARRAY modifiers present! What is it?",
        "Other objects found, but no ARRAY modifiers present! What are they?": "Other objects found, but no ARRAY modifiers present! What are they?",
        "Other: {}": "Other: {}",
        "PUNCHit": "PUNCHit",
        "Parallel (all)": "Parallel (all)",
        "Partial": "Partial",
        "Path Preview": "Path Preview",
        "Pick Axis": "Pick Axis",
        "Planar Wedge Quad": "Planar Wedge Quad",
        "Planar Wedge Quad: {}": "Planar Wedge Quad: {}",
        "Planar Wedge Quad: {}": "Planar Wedge Quad: {}",
        "Plug Align": "Plug Align",
        "Plug Creator": "Plug Creator",
        "Plug Libraries": "Plug Libraries",
        "Plug Mode": "Plug Mode",
        "Plug Packs": "Plug Packs",
        "Plug Precision": "Plug Precision",
        "Plug Resources": "Plug Resources",
        "Plug Settings": "Plug Settings",
        "Plug Utils": "Plug Utils",
        "Plug handle does not seem to have a plug object as a child": "Plug handle does not seem to have a plug object as a child",
        "Plug is missing a 'conform' vertex group.": "Plug is missing a 'conform' vertex group.",
        "Plug": "Plug",
        "Plug": "Plug",
        "Plug: %s": "Plug: %s",
        "Plugged": "Plugged",
        "Plugs can't just consist of a handle, they need to have a plug mesh as well.": "Plugs can't just consist of a handle, they need to have a plug mesh as well.",
        "Plugs": "Plugs",
        "Points": "Points",
        "Precision": "Precision",
        "Project": "Project",
        "Projected Face Interpolated": "Projected Face Interpolated",
        "Projected Loops": "Projected Loops",
        "Propagate": "Propagate",
        "Property": "Property",
        "Quad Corner": "Quad Corner",
        "Quick Patch": "Quick Patch",
        "Radius": "Radius",
        "Raycast": "Raycast",
        "Reach": "Reach",
        "Real Mirror": "Real Mirror",
        "Rebuild": "Rebuild",
        "Reconstruct Chamfer from Fillet/rounded Bevel": "Reconstruct Chamfer from Fillet/rounded Bevel",
        "Reconstruct hard edge from Bevel by using Unfuse + Unchamfer in sequence": "Reconstruct hard edge from Bevel by using Unfuse + Unchamfer in sequence",
        "Reconstruct hard edge from Chamfer": "Reconstruct hard edge from Chamfer",
        "Redefine Wedge Area": "Redefine Wedge Area",
        "Redefine Wedge Area": "Redefine Wedge Area",
        "Redirect Chamfer Flow by turning a corner where 3 Chamfers meet": "Redirect Chamfer Flow by turning a corner where 3 Chamfers meet",
        "Refuse": "Refuse",
        "Regular": "Regular",
        "Regular": "Regular",
        "Relax": "Relax",
        "Reload all libraries. Propagates forced lock settings.\nSave prefs to complete the process": "Reload all libraries. Propagates forced lock settings.\nSave prefs to complete the process",
        "Remove Plug Library": "Remove Plug Library",
        "Remove Plug": "Remove Plug",
        "Remove Plugs": "Remove Plugs",
        "Remove Redundant Center": "Remove Redundant Center",
        "Remove Sources": "Remove Sources",
        "Remove VGroup": "Remove VGroup",
        "Remove Vertex Group": "Remove Vertex Group",
        "Remove selected plug library and all its plugs": "Remove selected plug library and all its plugs",
        "Remove": "Remove",
        "Removing a plug deletes it from the hard drive, this cannot be undone!": "Removing a plug deletes it from the hard drive, this cannot be undone!",
        "Rename Plug Library": "Rename Plug Library",
        "Rename selected library": "Rename selected library",
        "Replace": "Replace",
        "Resample": "Resample",
        "Reset normals of the selected geometry, keep unselected geo as is": "Reset normals of the selected geometry, keep unselected geo as is",
        "Retrieve and Re-Stash": "Retrieve and Re-Stash",
        "Retrieved": "Retrieved",
        "Reverse Plug Sorting (requires library reload or Blender restart)": "Reverse Plug Sorting (requires library reload or Blender restart)",
        "Reverse Plug Sorting": "Reverse Plug Sorting",
        "Reverse Plug Sorting": "Reverse Plug Sorting",
        "Reverse": "Reverse",
        "Reverse": "Reverse",
        "Rotation": "Rotation",
        "Run VSelect, SSelect, LSelect or pass through to Blender": "Run VSelect, SSelect, LSelect or pass through to Blender",
        "SHIFT scroll UP/DOWN": "Shift scroll UP/DOWN",
        "SSelect": "SSelect",
        "Scale": "Scale",
        "Segments": "Segments",
        "Select all sharp edges connected to the existing selection": "Select all sharp edges connected to the existing selection",
        "Select plug handle and a target object to plug into.": "Select plug handle and a target object to plug into.",
        "Select": "Select",
        "Selected 2 faces are not next to each other!": "Selected 2 faces are not next to each other!",
        "Selected only: {}": "Selected only: {}",
        "Selected": "Selected",
        "Selection Index": "Selection Index",
        "Selection does not have 3 corners, it's not a triangular corner, aborting": "Selection does not have 3 corners, it's not a triangular corner, aborting",
        "Selection does not include faces, aborting": "Selection does not include faces, aborting",
        "Selection has less than 2 faces, aborting": "Selection has less than 2 faces, aborting",
        "Selection has less than 3 verts selected, aborting": "Selection has less than 3 verts selected, aborting",
        "Selection has less than 6 verts, aborting": "Selection has less than 6 verts, aborting",
        "Selection includes ngons, aborting": "Selection includes ngons, aborting",
        "Selection includes tris, aborting": "Selection includes tris, aborting",
        "Selection is cyclic, aborting": "Selection is cyclic, aborting",
        "Selection is not a chamfer, aborting": "Selection is not a chamfer, aborting",  #
        "Selection is not a polysrip, aborting": "Selection is not a polysrip, aborting",
        "Selection need to be at least 3 loop edges, aborting": "Selection need to be at least 3 loop edges, aborting",
        "Selection": "Selection",
        "Self Stash": "Self Stash",
        "Set BWeights": "Set BWeights",
        "Set Bevel Weights": "Set Bevel Weights",
        "Set Cursor to Stash": "Set Cursor to Stash",
        "Set E/F": "Set E/F",
        "Set Plug Props": "Set Plug Props",
        "Set Sharps": "Set Sharps",
        "Set/Change Plug properties": "Set/Change Plug properties",
        "Shade Smooth": "Shade Smooth",
        "Shift": "Shift",
        "Show Delete Menu": "Show Delete Menu",
        "Show Hints": "Show Hints",
        "Show Indicators": "Show Indicators",
        "Show LoopTools Wrappers": "Show LoopTools Wrappers",
        "Show Mesh Split tool": "Show Mesh Split tool",
        "Show Plug Buttons below Libraries": "Show Plug Buttons below Libraries",
        "Show Plug Count next to Library Name": "Show Plug Count next to Library Name",
        "Show Plug Name on Insert Button": "Show Plug Name on Insert Button",
        "Show Plug Name on Insert Buttons": "Show Plug Name on Insert Buttons",
        "Show Plug Names in Decal Libraries": "Show Plug Names in Decal Libraries",
        "Show Plug Names in Plug Libraries": "Show Plug Names in Plug Libraries",
        "Show Plugs 'In Front' when bringing them into the scene": "Show Plugs 'In Front' when bringing them into the scene",
        "Show Sidebar Panel": "Show Sidebar Panel",
        "Show Wire": "Show Wire",
        "Show in Context Menus": "Show in Context Menus",
        "Show in Edit Mode Context Menu": "Show in Edit Mode Context Menu",
        "Show in Object Mode Context Menu": "Show in Object Mode Context Menu",
        "Side A": "Side A",
        "Side B": "Side B",
        "Side Select": "Side Select",
        "Side": "Side",
        "Simple": "Simple",
        "Single": "Single",
        "Size of Icons in Plug Libaries": "Size of Icons in Plug Libaries",
        "Size of Icons in Plug Libraries": "Size of Icons in Plug Libraries",
        "Size of Plug Libary Icons": "Size of Plug Libary Icons",
        "Size of Plug Library Icons": "Size of Plug Library Icons",
        "Slide Amount": "Slide Amount",
        "Slide Wedge Corner: {}": "Slide Wedge Corner: {}",
        "Slide Wedge Corner: {}": "Slide Wedge Corner: {}",
        "Slide": "Slide",
        "Slide": "Slide",
        "Smooth": "Smooth",
        "Solver": "Solver",
        "Something went wrong, likely not a valid chamfer selection.": "Something went wrong, likely not a valid chamfer selection.",
        "Split": "Split",
        "Spread": "Spread",
        "Stash Index": "Stash Index",
        "Stash Matrix": "Stash Matrix",
        "Stash Object": "Stash Object",
        "Stash Operants": "Stash Operants",
        "Stash Original": "Stash Original",
        "Stash it": "Stash it",
        "Stash the current state of an object": "Stash the current state of an object",
        "Stash the current state of the entire mesh or only the selected faces": "Stash the current state of the entire mesh or only the selected faces",
        "Stash the current state of the object\nALT: Stash the evaluated mesh of the current object": "Stash the current state of the object\nALT: Stash the evaluated mesh of the current object",
        "Stash them": "Stash them",
        "Stash": "Stash",
        "Stash": "Stash",
        "Stashes HUD Offset": "Stashes HUD Offset",
        "Stashes HUD offset": "Stashes HUD offset",
        "Stashes Offset": "Stashes Offset",
        "Stashes: ": "Stashes: ",
        "Steps": "Steps",
        "Straighten uneven shading on straight fuse surface sections": "Straighten uneven shading on straight fuse surface sections",
        "Straighten": "Straighten",
        "Subdivisions": "Subdivisions",
        "Subset Precision": "Subset Precision",
        "Subset": "Subset",
        "Subset: %s": "Subset: %s",
        "Subsets": "Subsets",
        "Surface Point": "Surface Point",
        "Swap": "Swap",
        "Sweep Stashes": "Sweep Stashes",
        "Sweep up stash objects, that became visible aber appending objects from other blend files": "Sweep up stash objects, that became visible aber appending objects from other blend files",
        "Symmetrize a mesh incl. its custom normals": "Symmetrize a mesh incl. its custom normals",
        "Symmetrize": "Symmetrize",
        "Tabs": "Tabs",
        "Taper Flip": "Taper Flip",
        "Taper": "Taper",
        "Taper": "Taper",
        "Target Matrix": "Target Matrix",
        "Target Normal": "Target Normal",
        "Tension 2": "Tension 2",
        "Tension Linked": "Tension Linked",
        "Tension Presets": "Tension Presets",
        "Tension": "Tension",
        "Tension": "Tension",
        "The Imported Plug doesn't not contain a valid Plug Handle.": "The Imported Plug doesn't not contain a valid Plug Handle.",
        "The new name needs to be different from the old one.": "The new name needs to be different from the old one.",
        "The selected object is not a (valid) plug handle, aborting": "The selected object is not a (valid) plug handle, aborting",
        "There's a non-manifold edge closeby, failed to determine sides of the selection.": "There's a non-manifold edge closeby, failed to determine sides of the selection.",
        "This library exists already, choose another name!": "This library exists already, choose another name!",
        "This removes the plug '%s' from library '%s'!": "This removes the plug '%s' from library '%s'!",
        "This removes the plug library '%s' and all its plugs!": "This removes the plug library '%s' and all its plugs!",
        "Threshold": "Threshold",
        "Time (s)": "Time (s)",
        "Time": "Time",
        "Timeout": "Timeout",
        "Tools": "Tools",
        "Transfer Normals from Stash": "Transfer Normals from Stash",
        "Transfer Stashes from one object to another": "Transfer Stashes from one object to another",
        "Transfer Stashes": "Transfer Stashes",
        "Transfer": "Transfer",
        "Transformation": "Transformation",
        "Triangulate": "Triangulate",
        "True": "True",
        "Turn Corner": "Turn Corner",
        "Turn isolated edge preselections into loop selections with control overthe loop-angle": "Turn isolated edge preselections into loop selections with control overthe loop-angle",
        "Turn them into Quad Corners first!": "Turn them into Quad Corners first!",
        "Turn": "Turn",
        "Turn": "Turn",
        "Tutorial": "Tutorial",
        "U": "U",
        "UI": "UI",
        "UUID: %s": "UUID: %s",
        "UV Offset": "UV Offset",
        "Unbevel": "Unbevel",
        "Unchamfer Method": "Unchamfer Method",
        "Unchamfer": "Unchamfer",
        "Unf*ck": "Unf*ck",
        "Unfuse": "Unfuse",
        "Union": "Union",
        "Unsaved changes will be lost,": "Unsaved changes will be lost,",
        "Up": "Up",
        "Update is available": "Update is available",
        "Use Deformer": "Use Deformer",
        "Use Experimental Features, at your own risk": "Use Experimental Features, at your own risk",
        "Use Legacy Line Smoothing": "Use Legacy Line Smoothing",
        "User Plug Libraries": "User Plug Libraries",
        "User Plug Library Index": "User Plug Library Index",
        "VIEW_3D": "VIEW_3D",
        "VSelect": "VSelect",
        "Validate Plug": "Validate Plug",
        "Validate and Debug a Plug Asset": "Validate and Debug a Plug Asset",
        "Vert mode": "Vert mode",
        "Vertex Group": "Vertex Group",
        "View 3D": "View 3D",
        "View Oprhan Stashes": "View Oprhan Stashes",
        "View Orphan Stashes": "View Orphan Stashes",
        "View Stash": "View Stash",
        "View Stashes": "View Stashes",
        "View stashes of an object and retrieve, edit or clear them": "View stashes of an object and retrieve, edit or clear them",
        "Viewport": "Viewport",
        "Visually select mesh elements by Vertex Group": "Visually select mesh elements by Vertex Group",
        "Web": "Web",
        "Wedge": "Wedge",
        "Weight": "Weight",
        "Width (experimental)": "Width (experimental)",
        "Width 2": "Width 2",
        "Width Linked": "Width Linked",
        "Width": "Width",
        "Width": "Width",
        "Wiggle": "Wiggle",
        "X-Ray": "X-Ray",
        "You can't unfuse bevels with triangular coners": "You can't unfuse bevels with triangular coners",
        "Your current file is not saved!": "Your current file is not saved!",
        "Youtube": "Youtube",
        "debug HUD": "debug HUD",
        "description": "description",
        "from {} itself": "from {} itself",
        "from {}": "from {}",
        "from {}'s selected faces": "from {}'s selected faces",
        "has fillet": "has fillet",
        "if you load the following example.": "if you load the following example.",
        "in Edit Mode": "in Edit Mode",
        "in Object Mode": "in Object Mode",
        "is plug deformer": "is plug deformer",
        "is plug handle": "is plug handle",
        "is plug occluder": "is plug occluder",
        "is plug subset": "is plug subset",
        "is plug": "is plug",
        "is plug": "is plug",
        "is stash object": "is stash object",
        "move LEFT/RIGHT": "move LEFT/RIGHT",
        "move LEFT/RIGHT, toggle W": "move LEFT/RIGHT, toggle W",
        "move LEFT/RIGHT, toggle W, reset ALT + W": "move LEFT/RIGHT, toggle W, reset ALT + W",
        "move LEFT/RIGHT, toggle W, reset ALT + W, presets Z/Y, X, C, V, B": "move LEFT/RIGHT, toggle W, reset ALT + W, presets Z/Y, X, C, V, B",
        "move LEFT/RIGHT, toggle W, rest ALT + W": "move LEFT/RIGHT, toggle W, rest ALT + W",
        "move UP/DOWN, toggle I, reset ALT + I": "move UP/DOWN, toggle I, reset ALT + I",
        "move UP/DOWN, toggle T, presets Z/Y, X, C, V": "move UP/DOWN, toggle T, presets Z/Y, X, C, V",
        "normal_transfer": "normal_transfer",
        "plug uuid": "plug uuid",
        "press A": "press A",
        "press C": "press C",
        "press D": "press D",
        "press E": "press E",
        "press R": "press R",
        "press S": "press S",
        "press SHIFT + ESC to finish": "press SHIFT + ESC to finish",
        "reqiures library reload or Blender restart": "reqiures library reload or Blender restart",
        "scroll UP/DOWN": "scroll UP/DOWN",
        "scroll UP/DOWN": "scroll UP/DOWN",
        "scroll UP/DOwn": "scroll UP/DOwn",
        "scroll UP/Down": "scroll UP/Down",
        "show in Context Menus": "show in Context Menus",
        "stash name": "stash name",
        "stash uuid": "stash uuid",
        "stash version": "stash version",
        "to %s": "to %s",
        "toggle A": "toggle A",
        "toggle B": "toggle B",
        "toggle B": "toggle B",
        "toggle C": "toggle C",
        "toggle D": "toggle D",
        "toggle D": "toggle D",
        "toggle F": "toggle F",
        "toggle M": "toggle M",
        "toggle P": "toggle P",
        "toggle Q": "toggle Q",
        "toggle R": "toggle R",
        "toggle S": "toggle S",
        "toggle T": "toggle T",
        "toggle V": "toggle V",
        "toggle X": "toggle X",
        "toggle X": "toggle X",
        "toggle X": "toggle X",
    },
    "zh_CN": {
        "Language": "语言",
        "Chat Room": "免费加 QQ 学习群",
        "Buy More": "更新插件",
        "Language Description": "切换到中文界面时, 请在“编辑-偏好设置-界面-翻译”里取消勾选“新建数据”!!!",
        "Watch online video tutorials": "查看视频教程",
        "Translation is in progress. Whole text isn't translated": "翻译进行中, 优化纠错请联系 iBlender®taobao.com",
        "This keymap should be the same as your native Blender Loop Select Keymapping": "该快捷键应与本地 Blender 循环选择键相同",
        "If you don't use ALT + LMB for loop selecting, remap this accordingly!": "如果您不使用 ALT + 左键 进行循环选择, 请相应地重新设置此快捷键！",
        "Mirror Vertex Groups": "镜像顶点组",
        "Label": "标签",
        "Object Mode": "对象模式",
        "Redundant Threshold Angle": "冗余阈值角度",
        "Always Loop Select": "总是环选",
        "Edit Mode": "编辑模式",
        "    • Force Deform: %s ": "• 强制形变: %s",
        "  • Force Deform: %s ": "• 强制形变: %s",
        "  • Type: %s": "• 类型: %s",
        "  • Use Deformer: %s": "• 使用形变器: %s",
        "(B) Unbevel": "(B) 反倒角",
        "(C) Unchamfer": "(C) 反平角",
        "(D) Unfuse": "(D) 反融合",
        "(E) Flatten": "(E) 展平",
        "(R) Refuse": "(R) 再融合",
        "(W) Change Width": "(W) 改变宽度",
        "(X) Delete Plug": "(X) 删除零件",
        "(X) Delete Plugs": "(X) 删除零件",
        "(X) Delete": "(X) 删除",
        "(X) Unf*ck": "(X) Unf*ck",
        "(Y) Delete Plug": "(Y) 删除零件",
        "(Y) Delete Plugs": "(Y) 删除零件",
        "(Y) Split": "(Y) 拆分",
        "+ to -": "+ 到 -",
        "- to +": "- 到 +",
        "3D Cursor": "3D 游标",
        "A new version is available!": "有新版本可用!!",
        "ALT scroll  UP/DOWN": "Alt 上/下滚轮",
        "ALT scroll UP/DOWN": "Alt 上/下滚轮",
        "About": "关于",
        "Actions": "动作",
        "Add Boolean Modifier": "添加布尔修改器",
        "Add Boolean": "添加布尔",
        "Add Mode": "添加模式",
        "Add Plug to Library": "添加零件到库",
        "Add a new empty library": "添加一个空的零件库",
        "Add selected Plug to Plug Library": "增加选中的零件到零件库",
        "Advanced Mode": "高级模式",
        "Advanced": "高级",
        "Align Mode": "对齐模式",
        "Align non-cyclic edge loop along bezier": "沿着贝塞尔曲线对齐非循环边缘环",
        "All": "全部",
        "Along Edge": "沿着边缘",
        "Along Normal": "沿着法线",
        "Alpha": "Alpha",
        "Also Clear Vertex Groups": "还清除顶点组",
        "Amount": "数量",
        "Angle Threshold Presets": "角度阈值预设",
        "Angle Threshold": "角度阈值",
        "Angle": "角度",
        "Appereance": "表现",
        "Apply All Modifiers": "应用所有修改器",
        "Apply Booleans": "应用布尔",
        "Apply Data Transfers": "应用数据传输",
        "Apply Mod": "应用修改器",
        "Apply Normal Transfer": "应用法线传递",
        "Apply Shrink Wrap": "应用缩裹",
        "Apply all Boolean Modifiers, and stash the Cutters": "应用所有布尔修改器, 并隐藏切割器",
        "Are you sure? This cannot be undone!": "你确定吗？此操作无法撤销!",
        "Asset Loaders": "资产导入器",
        "Auto-Smooth": "自动平滑",
        "Auto-X-Ray the plug and its subsets, when inserting Plug into scene": "场景插入零件时, 自动透视零件及其子集",
        "Average Tension": "平均张力",
        "Axis": "轴向",
        "BWeight": "倒角权重",
        "Basic": "基础",
        "Best fit": "最佳适配",
        "Blender Addons": "Blender 插件",
        "Blender Market": "Blender Market",
        "Boolean Cleanup": "布尔清理",
        "Bridge": "桥",
        "CTRL scroll UP/DOWN": "Ctrl 滚轮上/下",
        "CURVEmachine": "CURVEmachine",
        "Cancel": "取消",
        "Cap Holes": "封孔",
        "Cap": "端点",
        "Careful, values above 4 are increasingly slow": "小心, 大于 4 会极慢",
        "Chamfer cyclic selections resulting from Boolean Operations": "由布尔运算产生的循环选择面倒角",
        "Chamfer": "平角",
        "Change Width": "改变宽度",
        "Change the width of Chamfers(flat Bevels)": "改变倒角的宽度（平面斜角）",
        "Change this, so Plugs created by you, are tagged with your info!": "留下你的大名, 世界就是你的了!",
        "Circle": "圆环",
        "Clear Center Sharps": "清除中心锐边",
        "Clear Existing Normals": "清除现有法线",
        "Clear Loop": "清除循环",
        "Clear Normals": "清除法线",
        "Clear Plug Props": "清除零件属性",
        "Clear Plug properties": "清除零件属性",
        "Clear library prefs, resets them into their original state.\nNo plugs will be lost!\nSave prefs and restart Blender to complete the process": "清除库偏好设置, 将其重置为原始状态。\n不会丢失任何插花！保存偏好设置并重启 Blender 以完成该过程",
        "Clear": "清除",
        "Color": "颜色",
        "Conform selection to Stash surface": "适配选中到隐藏物表面",
        "Conform": "贴合",
        "Contain": "包含",
        "Convert Mirrod Modifiers into real geometry with proper origins and properly mirrored custom normals": "将Mirrod Modifiers转换为具有适当原点和正确镜像的自定义法线的实际几何体",
        "Convert a triangular Bevel Corner to Quad Corner": "将三边形倒角转角转换为四边形倒角转角",
        "Corner": "夹角",
        "Couldn't find 2 quads on either side of the selection!": "在选择的任一侧都找不到 2 个四边形!",
        "Countdown (s)": "倒数 (s)",
        "Create Plug from mesh object(s)": "从网格对象创建插入零件",
        "Create Plug": "创建零件",
        "Create RealMirror Collections": "创建真镜像集合",
        "Create Stash": "创建暂存",
        "Create Vertex Group": "创建顶点组",
        "Create Vertex Groups": "创建顶点组",
        "Create Wedge from Edge Selection": "从边选择创建楔形",
        "Create a surface confirming polygon patch by drawing 4 corner points.": "通过绘制 4 个拐角点来创建确认多边形面片的曲面。",
        "Create rounded Bevels from Chamfers": "从倒角创建圆倒角平倒角",
        "Creator: %s": "创建者: %s",
        "Cubic": "立方",
        "Custom Normal Mirror Method": "自定义法线镜像法",
        "Custom Normal Pairing Method": "自定义法线配对方法",
        "Custom Radius": "自定义半径",
        "Custom": "自定义",
        "Cyclic selections are not supported, aborting": "不支持循环选择，中止",
        "Cyclic": "循环",
        "DECALmachine": "DECALmachine",
        "DEFORMER": "形变器",
        "Debug Whatever": "调试",
        "Debug": "调试",
        "Define Wedge Area": "定义楔形区域",
        "Define Wedge Depth": "定义楔形深度",
        "Deform Plug": "形变零件",
        "Deform Subsets": "形变子集",
        "Deformation": "形变",
        "Deformer Precision": "形变精度",
        "Deformer": "形变器",
        "Deformer: %s": "形变器: %s",
        "Delete All": "全部删除",
        "Delete Handle, Plug and all Support Objects": "删除控制柄, 零件和全部支持对象",
        "Delete": "删除",
        "Deleting All": "全部删除",
        "Deleting Marked": "删除标记的",
        "Delta Matrix": "Delta 矩阵",
        "Depth Amount": "深度数量",
        "Difference": "差集",
        "Direction": "方向",
        "Display": "显示",
        "Dissolve Angle": "消融角度",
        "Dissolve": "消融",
        "Docs": "官方文档",
        "Documentation": "官方文档",
        "Documention": "官方文档",
        "Down": "下",
        "Draw Active Stash in 3D View": "在 3D 视图中绘制活动暂存",
        "Draw Active Stash in X-Ray": "在透视中绘制活动暂存",
        "Draw Debug": "绘制调试",
        "Draw Properties": "绘制属性",
        "Draw Timer": "显示计时器",
        "Duplicate Boolean Objects with their Cutters\nALT: Instance the Object and Cutter Meshes": "使用切割器复制布尔对象\nALT：实例化对象和切割网格",
        "Duplicate Booleans": "复制布尔",
        "Edge Index": "边索引",
        "Edge": "边",
        "Edit Fillets by using Unfuse + Fuse in sequence": "(可以对已经成型的倒角再次编辑) Edit Fillets by using Unfuse + Fuse in sequence",
        "Edit": "编辑",
        "Editing %s": "编辑 %s",
        "Embed Plug into mesh surface": "将零件嵌入网格表面",
        "Empties found, but no ARRAY or HOOK modifiers present!": "发现空对象, 但无阵列或钩挂修改器!",
        "Empty found, but no ARRAY or HOOK modifiers present!": "发现空对象, 但无阵列或钩挂修改器!",
        "Empty: %s": "空: %s",
        "Exact": "精确",
        "Examples": "示例",
        "Experimental Features": "实验功能",
        "Experimental": "实验性",
        "Extend": "延伸",
        "Extra": "其它",
        "FAQ": "常见问题",
        "FILLET or EDGE": "圆角或边",
        "Face Method": "面方法",
        "Face Mode": "面模式",
        "Face": "面",
        "Factor": "系数",
        "Fade": "渐隐",
        "Fading wire frames (experimental)": "褪色线框（实验功能)",
        "Failed to rename library": "重命名库失败",
        "False": "关",
        "Fast": "快速",
        "Fillet or Edge": "圆角或边",
        "Fillet": "圆角",
        "Finish and select Cutters": "完成并选择切割",
        "Finish": "完成",
        "Fit inside": "适配内部",
        "Fix Center Method": "修复中心方法",
        "Fix Center Seam": "修复中心接缝",
        "Fix Midpoint": "修复中点",
        "Flatten Along": "展平沿着",
        "Flatten Polygon(s) along Edges or Normal": "沿着边或法线展平多边形面",
        "Flatten uneven shading on (mostly) ngons": "在多边面上展平不均匀的着色",
        "Flatten": "展平",
        "Flick Distance": "波动距离",
        "Flick": "波动",
        "Flip Red to Green": "红翻绿",
        "Flip Wedge Direction": "翻转楔形方向",
        "Flip": "翻转",
        "Flipped Normals": "翻转法线",
        "Flipped": "翻转",
        "Follow Mouse": "跟随鼠标",
        "Force Projected Loop": "强制投射循环",
        "Force Projected Loops": "强制投射循环",
        "Force Subset Deform": "强制子集形变",  # iBlender
        "Fuse": "融合",
        "General": "常规",
        "Generate Log Files and Instructions for a Support Request.": "生成日志文件和支持请求的说明.",
        "Generate new UUID": "新建通用唯一标识符",
        "Get Loops or Handles": "获取循环或者控制柄",
        "Get More Plugs": "获得更多零件",
        "HOOK or ARRAY": "钩挂或阵列",
        "HUD Color": "小窗颜色",
        "HUD Font Color": "小窗字体颜色",
        "HUD Scale": "提示小窗大小",
        "HUD": "提示小窗",
        "Handle contains N-Gons!": "控制柄包含多边形!",
        "Handle polygons are flipped!": "控制柄多边形被翻转!",
        "Handle": "控制柄",
        "Handle: %s": "控制柄: %s",
        "Handles": "控制柄",
        "Has Fillet": "圆角",
        "Have you already removed it manualy, while Blender was running?": "是否是你在 Blender 运行时手动删除了它?",
        "Help": "帮助",
        "Hide Deformer and Occluder and Others": "隐藏形变和遮挡器等",
        "Illegal Selection": "非法选择",
        "Index": "索引",
        "Influence": "影响",
        "Info": "提示",
        "Initial Run": "初始化运行",
        "Initialize": "初始化",
        "Input": "输入",
        "Insert Plug": "插入零件",
        "Insert": "插入",
        "Instance": "实例",
        "Integration": "积分",
        "Interpolation Falloff (Surface Deform)": "插值衰减（表面形变)",
        "Interpolation": "插值",
        "Intersect": "相交",
        "Invalid Plug": "Plug 无效",
        "Invalid Selection": "选择无效",
        "Iterations": "迭代",
        "Keymaps": "键位映射",
        "LMB to place, F1 to undo, F2 to redo": "鼠标左键: 放置, F1: 撤消, F2: 重做",
        "LOCKED": "锁定",
        "LSelect": "循环选择",
        "Leave empty, to disable": "留空以禁用",
        "Length": "长度",
        "Libraries": "库",
        "Library path could not be found, reload libraries or restart Blender.": "未找到库路径, 请重新加载库或重启 Blender。",
        "Library": "库",
        "Limit by Sharps": "按锐边限制",
        "Limit to Selection": "限制到选择",
        "Linear": "线性",
        "Linked": "已关联",
        "Local": "局部",
        "Location": "位置",
        "Lock X": "锁定 X",
        "Lock Y": "锁定 Y",
        "Lock Z": "锁定 Z",
        "Loop Select": "循环选择",
        "Loop Slide ": "循环滑移 ",
        "Loop Slide": "环移",
        "Loop edges don't intersect.": "循环边不相交。",
        "Loop": "循环",
        "LoopTools": "LoopTools",
        "LoopTools' Circle as a modal": "LoopTools 的圆环作为模态",
        "LoopTools's Relax as a modal": "LoopTools 的 松弛 作为模态",
        "Loops": "循环",
        "MACHIN3": "M3",
        "MACHIN3: Add Plug Library": "MACHIN3: 添加零件库",
        "MACHIN3: Add Plug To Library": "MACHIN3: 添加零件到库",
        "MACHIN3: Boolean Apply": "MACHIN3: 布尔应用",
        "MACHIN3: Boolean Cleanup": "MACHIN3: 布尔清理",
        "MACHIN3: Boolean Duplicate": "MACHIN3: 布尔复制",
        "MACHIN3: Boolean": "MACHIN3: 布尔",
        "MACHIN3: Call MESHmachine Menu": "MESHmachine 菜单",
        "MACHIN3: Chamfer": "MACHIN3: 平角",
        "MACHIN3: Change Width": "MACHIN3: 更改宽度",
        "MACHIN3: Clear Plug Libraries": "MACHIN3: 清除零件库",
        "MACHIN3: Clear Plug Properties": "MACHIN3: 清除零件属性",
        "MACHIN3: Conform": "MACHIN3: 贴合",
        "MACHIN3: Create Plug": "MACHIN3: 创建零件",
        "MACHIN3: Create Stash": "MACHIN3: 创建暂存",
        "MACHIN3: Debug HUD": "MACHIN3: 调试小窗",
        "MACHIN3: Debug MESHmachine": "MACHIN3: 调试 MESHmachine",
        "MACHIN3: Debug Whatever": "MACHIN3: 上帝调试",
        "MACHIN3: Delete Plug": "MACHIN3: 删除零件",
        "MACHIN3: Draw Debug": "MACHIN3: 绘制调试",
        "MACHIN3: Draw Plug": "MACHIN3: 绘制零件",
        "MACHIN3: Draw RealMirror": "MACHIN3: 绘制真镜像",
        "MACHIN3: Draw Stash": "MACHIN3: 绘制暂存",
        "MACHIN3: Draw Symmetrize": "MACHIN3: 绘制对称",
        "MACHIN3: Draw Transferred Stashes": "MACHIN3: 绘制转移暂存",
        "MACHIN3: Flatten": "MACHIN3: 展平",
        "MACHIN3: Fuse": "MACHIN3: 融合",
        "MACHIN3: Get Angle": "MACHIN3: 获取角度",
        "Positive Z": "正 Z",
        "Negative X": "负 X",
        "FACE": "面",
        "Positive X": "正 X",
        "Apply Stashes Mods": "应用暂存修改器",
        "Apply All Mods Selected Objects": "应用所有修改器选定对象",
        "LOOP": "循环",
        "Apply Stash Mods": "应用暂存修改器",
        "No Boolean Mods found on Selected Objects": "在所选对象上未找到布尔修改器",
        "FUSE": "融合",
        "LoopTools activation failed!": "启用 LoopTools 失败！",
        "MACHIN3: Get Faces Linked to Verts": "MACHIN3: 获取与顶点相连的面",
        "MACHIN3: Get Length": "MACHIN3: 获取长度",
        "MACHIN3: Get MESHmachine Support": "MACHIN3: 获取 MESHmachine 支持",
        "MACHIN3: Get Sides": "MACHIN3: 获得滑移",
        "MACHIN3: Loop Select": "MACHIN3: 循环选择",
        "MACHIN3: LoopTools Circle": "MACHIN3: 循环工具圈环",
        "MACHIN3: LoopTools Relax": "MACHIN3: 循环工具松弛",
        "MACHIN3: Loops or Handles": "MACHIN3: 循环或控制柄",
        "MACHIN3: Make Unique": "MACHIN3: 唯一",
        "MACHIN3: Mark Loop": "MACHIN3: 标记循环边",
        "MACHIN3: Move Plug Library": "MACHIN3: 移动零件库",
        "MACHIN3: Normal Clear": "MACHIN3: 清除法线",
        "MACHIN3: Normal Flatten": "MACHIN3: 展平法线",
        "MACHIN3: Normal Straighten": "MACHIN3: 拉直传递",
        "MACHIN3: Normal Transfer": "MACHIN3: 传递法线",
        "MACHIN3: Offset Cut": "MACHIN3: 偏移切割",
        "MACHIN3: Offset": "MACHIN3: 偏移",
        "MACHIN3: Open Plug Library": "MACHIN3: 打开插花库",
        "MACHIN3: Plug": "MACHIN3: 零件",
        "MACHIN3: Quad Corner": "MACHIN3: 四边化夹角",
        "MACHIN3: Quick Patch": "MACHIN3: 快速面片",
        "MACHIN3: Real Mirror": "MACHIN3: 真镜像",
        "MACHIN3: Refuse": "MACHIN3: 再融合",
        "MACHIN3: Reload Plug Libraries": "MACHIN3: 重载零件库",
        "MACHIN3: Remove Stash": "MACHIN3: 删除暂存",
        "MACHIN3: Select": "MACHIN3: 选择",
        "MACHIN3: Set Plug Props": "MACHIN3: 设置零件属性",
        "MACHIN3: Sharp Select": "MACHIN3: 锐边选择",
        "MACHIN3: Swap Stash": "MACHIN3: 交换暂存",
        "MACHIN3: Sweep Stashes": "MACHIN3: 清扫暂存",
        "MACHIN3: Symmetrize": "MACHIN3: 对称",
        "MACHIN3: Transfer Stashes": "MACHIN3: 转移暂存",
        "MACHIN3: Turn Corner": "MACHIN3: 转向夹角",
        "MACHIN3: Unbevel": "MACHIN3: 反倒角",
        "MACHIN3: Unchamfer": "MACHIN3: 反平角",
        "MACHIN3: Unf*ck": "MACHIN3:反操蛋",
        "MACHIN3: Unfuse": "MACHIN3: 反融合",
        "MACHIN3: Validate Plug": "MACHIN3: 验证零件",
        "MACHIN3: Vertex Group Select": "MACHIN3: 顶点组选择",
        "MACHIN3: Vertex Info": "MACHIN3: 顶点信息",
        "MACHIN3: View Orphan Stashes": "MACHIN3: 查看孤零暂存",
        "MACHIN3: View Stashes": "MACHIN3: 查看暂存",
        "MACHIN3: Wedge": "MACHIN3: 楔形",
        "MACHIN3tools": "M3 工具",
        "MESHmachine Documentation": "MESHmachine 官方文档",
        "MESHmachine Help": "MESHmachine 帮助",
        "MESHmachine": "MESHmachine",
        "MOVE LEFT/RIGHT, toggle W, reset ALT + W": "移动 左/右, 切换 W, 重置 Alt + W",
        "Make Instanced Mesh Objects Unique incl. any Instanced Boolean Operators": "使实例网格对象独一无二, 包括任何实例化布尔操作",
        "Make Unique": "使唯一",
        "Make sure they aren't just ARRAY caps, that need to have their props cleared!": "确保它们不仅仅是阵列端点, 需要清理端点的属性!",
        "Make sure to select a single, manifold edge, with at least one end leading to exactly 2 other edges!": "确保选择有一端正好通向另外两条边的单一流形边!",
        "Mapping": "映射",
        "Mark Loop": "标记循环边",
        "Mark/Unmark edges for preferential treatement by Fuse/Refuse": "标记/取消标记边以便熔接/反熔接进行优先处理",
        "MatCap Switch": "切换材质捕获",
        "Matcap used for Surface Check.": "检查表面的材质捕获",
        "Menu": "菜单",
        "Merge Perimeter": "合并周长",
        "Merge verts on cyclic selections resulting from Boolean operations": "合并由布尔运算产生的循环选择的顶点",
        "Merge": "合并",
        "Mesh Deform": "网格形变",
        "Method": "方法",
        "Min Angle": "最小角度",
        "Mirror Custom Normals": "镜像自定义法线",
        "Modal": "模态",
        "Mode": "模式",
        "Mode: Symmstrize": "模式: 对称",
        "Modifier: %s": "修改器: %s",
        "Mouse Pointer": "鼠标指针",
        "Mouse Position for Plug Insertion": "插入零件的鼠标位置",
        "Move library up or down.\nThis controls the position in the MESHmachine Plug Libraries submenu.\nSave prefs to remember": "上下移动库。\n这控制 MESHmachine 插花库子菜单中的位置。\n调整后请保存偏好",
        "Multiple Creators": "多个创作者",
        "Multiple Deformers found!": "发现多个形变器!",
        "Multiple Empties": "多个空对象",
        "Multiple Empties:": "多个空对象: ",
        "Multiple Handles found!": "找到多个控制柄!",
        "Multiple Modifiers:": "多个修改器: ",
        "Multiple Occluders found!": "发现多个遮挡物!",
        "Multiple Others:": "多个其它: ",
        "Multiple Plug Meshes found!": "发现多个零件网格!",
        "Multiple Subsets": "多个子集",
        "Multiple Subsets:": "多个子集: ",
        "Multiple UUIDs": "多个通用唯一标识符",
        "My other Blender Addons": "我的其它插件",
        "Name (optiona)": "名称（可选)",
        "Name (optional)": "名称(可选)",
        "Nearest Corner and Best Matching Face Normal": "最近的夹角和最佳匹配面法线",
        "Nearest Corner and Best Matching Normal": "最近的夹角和最佳匹配法线",
        "Nearest Face Interpolated": "最近插值面",
        "Nearest Vertex": "最近顶点",
        "New Library Name": "新建库名称",
        "New Name": "新名称",
        "New Name:": "新名称:",
        "New": "新",
        "No Handle found!": "未发现控制柄!",
        "No Plug Mesh found!": "未发现零件网格!",
        "No active object in selection.": "选择中无活动对象.",
        "No active object!": "无活动对象!",
        "No new name chosen.": "未选择新名称.",
        "Non-Manifold Geometry": "非流形几何",
        "Non-manifold edges are part of the selection. Failed to determine sides of the selection.": "非流形边缘是选择的一部分. 无法确定选择的边.",
        "None": "无",
        "Normal Flatten": "展平法线",
        "Normal Transfer Matcap": "法线传递材质捕获",
        "Normal Transfer": "法线传递",
        "Normals": "法线",
        "Not covered by Product Support!": "风险自负!",
        "Objet Mode": "对象模式",
        "Occluder": "遮挡",
        "Occluder: %s": "遮挡物: %s",
        "Offset Cut": "偏移切割",
        "Offset Patch": "面片偏移",
        "Offset cyclic edgeloops resulting from Boolean Operations to create a perimeter loop": "从布尔操作对象中的交叉部位循环边偏移",
        "Offset": "偏移",
        "Old Name:": "旧名称: ",
        "Online": "在线",
        "Only single-island cyclic or non-cyclic edge loop selections are supproted.": "仅支持单岛循环或非循环边循环选择。",
        "Open selected library in file browser": "在文件浏览器里打开选中的零件库",
        "Optimize": "优化",
        "Orphan Matrix": "孤零矩阵",
        "Other object found, but no ARRAY modifiers present! What is it?": "发现其它对象, 但没有阵列修改器预设! 它是啥啥东西?",
        "Other objects found, but no ARRAY modifiers present! What are they?": "发现其它对象, 但是没有阵列修改器预设! 它是啥东西?",
        "Other: {}": "其它",
        "Parallel (all)": "平行 (全部)",
        "Partial": "部分",
        "Path Preview": "路径预览",
        "Pick Axis": "拾取轴向",
        "Planar Wedge Quad": "平面楔形四边形",
        "Planar Wedge Quad: {}": "平面楔形四边形: {}",
        "Plug Align": "对齐到:",
        "Plug Creator": "零件创造者",
        "Plug Libraries": "零件库",
        "Plug Mode": "零件模式",
        "Plug Packs": "更多零件资产包",
        "Plug Precision": "零件精度",
        "Plug Resources": "零件资源",
        "Plug Settings": "零件设置",
        "Plug Utils": "零件工具箱",
        "Plug handle does not seem to have a plug object as a child": "Plug 控制柄似乎没有做子级的零件对象",
        "Plug is missing a 'conform' vertex group.": "Plug 缺少“贴合”顶点组。",
        "Plug": "零件",
        "Plug: %s": "零件: %s",
        "Plugged": "官方范例",
        "Plugs can't just consist of a handle, they need to have a plug mesh as well.": "零件不能只有一个控制柄组成，它们还需要有一个零件网格.",
        "Plugs": "零件",
        "Points": "点",
        "Precision": "精度",
        "Project": "投射",
        "Projected Face Interpolated": "投射插值面",
        "Projected Loops": "投射循环",
        "Propagate": "传递",
        "Property": "属性",
        "Quad Corner": "四边化夹角",
        "Quick Patch": "快速面片",
        "Radius": "半径",
        "Raycast": "光线投射",
        "Reach": "达到",
        "Real Mirror": "真镜像",
        "Rebuild": "重构",
        "Reconstruct Chamfer from Fillet/rounded Bevel": "从圆形倒角重建倒角",
        "Reconstruct hard edge from Bevel by using Unfuse + Unchamfer in sequence": "通过依次使用Unfuse + Unchamfer从斜角重建硬边",
        "Reconstruct hard edge from Chamfer": "重建倒角的硬边",
        "Redefine Wedge Area": "重新定义楔形面积",
        "Redirect Chamfer Flow by turning a corner where 3 Chamfers meet": "通过转动 3 个倒角相交的拐角来重定向反平倒角流",
        "Refuse": "再融合",
        "Regular": "常规",
        "Relax": "松弛",
        "Reload all libraries. Propagates forced lock settings.\nSave prefs to complete the process": "重载所有库。强制锁定设置。保存偏好设置以完成该过程",
        "Remove Plug Library": "移除零件库",
        "Remove Plug": "移除零件",
        "Remove Plugs": "移除零件",
        "Remove Redundant Center": "移除冗余中心",
        "Remove Sources": "移除源",
        "Remove VGroup": "移除顶点组",
        "Remove Vertex Group": "移除顶点组",
        "Remove selected plug library and all its plugs": "移除选中的零件库及其所有零件",
        "Remove": "移除",
        "Removing a plug deletes it from the hard drive, this cannot be undone!": "从硬盘中删除零件, 无法挽回!",
        "Rename Plug Library": "重命名零件库",
        "Rename selected library": "重命名选中的零件库",
        "Replace": "更换",
        "Resample": "重采样",
        "Reset normals of the selected geometry, keep unselected geo as is": "在选中的几何体上重置法线,没选中的保持远洋",
        "Retrieve and Re-Stash": "取回和重新暂存",
        "Retrieve": "取回",
        "Retrieved": "取回",
        "Reverse Plug Sorting (requires library reload or Blender restart)": "反向排序零件（需要重新加载库或重启 Blender)",
        "Reverse Plug Sorting": "反向排序零件",
        "Reverse": "反向",
        "Rotation": "旋转",
        "Run VSelect, SSelect, LSelect or pass through to Blender": "运行顶点组选择, 锐边选择, 循环选择或传递到 Blender ",
        "SHIFT scroll UP/DOWN": "Shift 滚轮 上/下",
        "SSelect": "锐边选择",
        "Scale": "缩放",
        "Segments": "分段",
        "Select all sharp edges connected to the existing selection": "选择连接到现有选择的所有锐边",
        "Select plug handle and a target object to plug into.": "选择零件控制柄和目标对象以插入",
        "Select": "选择",
        "Selected 2 faces are not next to each other!": "选定的 2 个面不相接!",
        "Selected only: {}": "仅选择: {}",
        "Selected": "选定",
        "Selection Index": "选择索引",
        "Selection does not have 3 corners, it's not a triangular corner, aborting": "选择没有 3 个夹角, 它不是三角夹角，中止",
        "Selection does not include faces, aborting": "选择不包括面，中止",
        "Selection has less than 2 faces, aborting": "选择的面少于 2 个，中止",
        "Selection has less than 3 verts selected, aborting": "选择的顶点少于 3 个，正在中止",
        "Selection has less than 6 verts, aborting": "选择少于 6 个顶点，中止",
        "Selection includes ngons, aborting": "选择包括多边形，中止",
        "Selection includes tris, aborting": "选择包括三角面，中止",
        "Selection is cyclic, aborting": "选择是循环，中止",
        "Selection is not a chamfer, aborting": "选择非平角, 中止",
        "Selection is not a polysrip, aborting": "选择非 polysrip, 中止",
        "Selection need to be at least 3 loop edges, aborting": "需要选择至少 3 个循环边，中止",
        "Selection": "选择",
        "Self Stash": "自暂存",
        "Set BWeights": "设置倒角权重",
        "Set Bevel Weights": "设置倒角权重",
        "Set Cursor to Stash": "设置游标到暂存",
        "Set E/F": "设置 E/F",
        "Set Plug Props": "设置零件属性",
        "Set Sharps": "设置锐边",
        "Set/Change Plug properties": "设置/改变插入零件的属性",
        "Shade Smooth": "平滑着色",
        "Shift": "切换",
        "Show Delete Menu": "显示删除菜单",
        "Show Hints": "显示提示",
        "Show Indicators": "显示指示",
        "Show LoopTools Wrappers": "显示循环工具包裹",
        "Show Mesh Split tool": "显示网格分割工具",
        "Show Plug Buttons below Libraries": "在库下方显示零件按钮",
        "Show Plug Count next to Library Name": "在库名称旁边显示零件数",
        "Show Plug Name on Insert Button": "在内插按钮上显示零件名称",
        "Show Plug Name on Insert Buttons": "在内插按钮上显示零件名称",
        "Show Plug Names in Decal Libraries": "在贴花库中显示零件名称",
        "Show Plug Names in Plug Libraries": "在零件库中显示零件名称",
        "Show Plugs 'In Front' when bringing them into the scene": "导入零件到场景中时, 前置显示",
        "Show Sidebar Panel": "显示侧边栏面板",
        "Show Wire": "显示线",
        "Show in Context Menus": "在上下文菜单中显示",
        "Show in Edit Mode Context Menu": "在编辑模式上下文菜单中显示",
        "Show in Object Mode Context Menu": "在对象模式上下文菜单中显示",
        "Side A": "侧 A",
        "Side B": "侧 B",
        "Side Select": "侧选",
        "Side": "侧",
        "Simple": "简单",
        "Single": "单",
        "Size of Icons in Plug Libaries": "零件库中图标的大小",
        "Size of Icons in Plug Libraries": "零件库中图标的大小",
        "Size of Plug Libary Icons": "零件库图标大小",
        "Size of Plug Library Icons": "零件库图标大小",
        "Slide Amount": "滑移数量",
        "Slide Wedge Corner: {}": "滑移楔形夹角: {}",
        "Slide": "滑移",
        "Smooth": "平滑",
        "Solver": "解算器",
        "Something went wrong, likely not a valid chamfer selection.": "不对? 选了非有效平角?",
        "Split": "分割",
        "Spread": "扩散",
        "Stash Index": "暂存索引",
        "Stash Matrix": "暂存矩阵",
        "Stash Object": "暂存对象",
        "Stash Operants": "暂存操作",
        "Stash Original": "暂存原版",
        "Stash it": "暂存它",
        "Stash the current state of an object": "隐藏物一个对象的当前状态",
        "Stash the current state of the entire mesh or only the selected faces": "暂存整个网格或仅选定面的当前状态",
        "Stash the current state of the object\nALT: Stash the evaluated mesh of the current object": "暂存对象当前状态\nAlt: 暂存当前对象的评估网格",
        "Stash them": "暂存它们",
        "Stash": "暂存",
        "Stashes HUD Offset": "暂存小窗偏移",
        "Stashes HUD offset": "暂存小窗偏移",
        "Stashes Offset": "隐藏偏移",
        "Stashes: ": "暂存: ",
        "Steps": "步进",
        "Straighten uneven shading on straight fuse surface sections": "在选中的熔接面区域上拉直不均匀的着色(阴影怪异)",
        "Straighten": "拉直",
        "Subdivisions": "细分",
        "Subset Precision": "子集精度",
        "Subset": "子集",
        "Subset: %s": "子集: %s",
        "Subsets": "子集",
        "Surface Point": "曲面点",
        "Swap": "交换",
        "Sweep Stashes": "清除暂存",
        "Sweep up stash objects, that became visible aber appending objects from other blend files": "清理隐藏对象, 这些对象在附加来自其它混合文件的对象时变得可见",
        "Symmetrize a mesh incl. its custom normals": "根据网格对象自身的自定义法线来对称?",
        "Symmetrize": "对称",
        "Tabs": "标签",
        "Taper Flip": "翻转锥化",
        "Taper": "锥化",
        "Target Matrix": "目标矩阵",
        "Target Normal": "目标法线",
        "Tension 2": "张力 2",
        "Tension Linked": "张力链接",
        "Tension Presets": "张力预设",
        "Tension": "张力",
        "The Imported Plug doesn't not contain a valid Plug Handle.": "导入零件不包含有效零件控制柄。",
        "The new name needs to be different from the old one.": "新名称不能与旧名称相同",
        "The selected object is not a (valid) plug handle, aborting": "所选对象非 (有效的) 零件控制柄, 中止",
        "There's a non-manifold edge closeby, failed to determine sides of the selection.": "附近有非流形边, 无法确定选择的边",
        "This library exists already, choose another name!": "此库已存在, 请选择其它名称!",
        "This removes the plug '%s' from library '%s'!": "这将删除 “%s” 零件, 从库 “%s” 中 !",
        "This removes the plug library '%s' and all its plugs!": "这将删除插件库 “%s” 及其所有零件!",
        "Threshold": "阈值",
        "Time (s": "时间（秒",
        "Time (s)": "时间 (秒)",
        "Time": "时间",
        "Timeout": "超时",
        "Tools": "工具",
        "Transfer Normals": "传递法线",
        "Transfer Stashes from one object to another": "对象到对象间传递隐藏对象",
        "Transfer Stashes": "传递暂存",
        "Transfer": "传递",
        "Transformation": "转换",
        "Triangulate": "三角化",
        "True": "开",
        "Turn Corner": "转向夹角",
        "Turn isolated edge preselections into loop selections with control overthe loop-angle": "通过控制循环角度, 将孤立边预选转变为循环选择",
        "Turn them into Quad Corners first!": "请先把它们变成四边夹角",
        "Turn": "转换",
        "Tutorial": "教程",
        "UI": "用户界面",
        "UUID: %s": "通用唯一标识符: %s",
        "UV Offset": "UV 偏移",
        "Unbevel": "反倒角",
        "Unchamfer Method": "反平角方法",
        "Unchamfer": "反平角",
        "Unf*ck": "Unf*ck",
        "Unfuse": "反融合",
        "Union": "并集",
        "Unsaved changes will be lost,": "将丢失未保存的更改, ",
        "Up": "上",
        "Update is available": "有更新可用",
        "Use Deformer": "使用形变器",
        "Use Experimental Features, at your own risk": "试验功能, 送给喜欢吃螃蟹的",
        "Use Legacy Line Smoothing": "使用传统线条平滑",
        "User Plug Libraries": "用户零件库",
        "User Plug Library Index": "用户零件库索引",
        "VIEW_3D": "视图_3D",
        "VSelect": "顶点组选择",
        "Validate Plug": "验证零件",
        "Validate and Debug a Plug Asset": "验证和测试插入零件",
        "Vert mode": "顶点模式",
        "Vertex Group": "顶点组",
        "View 3D": "3D 视图",
        "View Oprhan Stashes": "显示孤立隐藏物",
        "View Orphan Stashes": "查看孤零暂存",
        "View Stash": "查看暂存",
        "View Stashes": "查看暂存",
        "View stashes of an object and retrieve, edit or clear them": "查看对象的隐藏对象并检索、编辑或清除它们",
        "Viewport": "视图",
        "Visually select mesh elements by Vertex Group": "通过顶点组可视化选择网格元素",
        "Web": "网络",
        "Wedge": "楔形",
        "Weight": "权重",
        "Width (experimental)": "宽度（实验功能)",
        "Width 2": "宽度 2",
        "Width Linked": "宽度",
        "Width": "宽度",
        "Wiggle": "扭动",
        "X-Ray": "投射",
        "You can't unfuse bevels with triangular coners": "无法反融合有三角化夹角的倒角",
        "Your current file is not saved!": "未保存当前文件!",
        "Youtube": "Youtube",
        "debug HUD": "调试 HUD",
        "from {} itself": "从 {} 自身",
        "from {}": "从 {}",
        "from {}'s selected faces": "从 {} 的选择面",
        "has fillet": "圆角",
        "if you load the following example.": "如果您加载以下示例",
        "in Edit Mode": "在编辑模式",
        "in Object Mode": "在对象模式",
        "is plug deformer": "零件变形器",
        "is plug handle": "零件控制柄",
        "is plug occluder": "零件遮挡",
        "is plug subset": "零件子集",
        "is plug": "零件",
        "is stash object": "暂存对象",
        "move LEFT/RIGHT": "移动 左/右",
        "move LEFT/RIGHT, toggle W": "移动 左/右, 切换 W",
        "move LEFT/RIGHT, toggle W, reset ALT + W": "移动 左/右, 切换 W, 重置 Alt + W",
        "move LEFT/RIGHT, toggle W, reset ALT + W, presets Z/Y, X, C, V, B": "移动 左/右, 切换 W, 重置 Alt + W, 预设 Z/Y, X, C, V, B",
        "move LEFT/RIGHT, toggle W, rest ALT + W": "移动 左/右, 切换 W, 重置 ALT + W",
        "move UP/DOWN, toggle I, reset ALT + I": "移动 上/下, 切换 I, 重置 Alt + I",
        "move UP/DOWN, toggle T, presets Z/Y, X, C, V": "移动 上/下, 切换 T, 预设 Z/Y, X, C, V",
        "plug uuid": "零件通用唯一识别码",
        "press A": "按 A",
        "press C": "按 C",
        "press D": "按 D",
        "press E": "按 E",
        "press R": "按 R",
        "press S": "按 S",
        "press SHIFT + ESC to finish": "按 Shift + ESC 结束",
        "reqiures library reload or Blender restart": "需要重新加载库或重启 Blender",
        "scroll UP/DOWN": "滚轮 上/下",
        "scroll UP/DOWN": "滚轮 上/下",
        "scroll UP/DOwn": "滚轮 上/下",
        "scroll UP/Down": "滚轮 上/下",
        "show in Context Menus": "显示在上下文菜单中",
        "stash name": "暂存名称",
        "stash uuid": "暂存通用唯一识别码",
        "stash version": "暂存版本",
        "to %s": "到 %s",
        "toggle A": "切换 A",
        "toggle B": "切换 B",
        "toggle C": "切换 C",
        "toggle D": "切换 D",
        "toggle F": "切换 F",
        "toggle M": "切换 M",
        "toggle P": "切换 P",
        "toggle Q": "切换 Q",
        "toggle R": "切换 R",
        "toggle S": "切换 S",
        "toggle T": "切换 T",
        "toggle V": "切换 V",
        "toggle X": "切换 X",
        "Addon": "插件",
        "Print Addon Registration Output in System Console": "在系统控制台上打印插件注册输出结果",
        "Name of Matcap used for Surface Check.": "用于表面检查的材质捕获名称。",
        "Show in Blender's Edit Mesh Context Menu": "在 Blender 的编辑网格上下文菜单中显示",
        "Leave Empty, to disable": "留空以禁用",
        "Show Mesh Split Tool": "显示网格分割工具",
        "Show in Blender's Object Context Menu": "在 Blender 的对象上下文菜单中显示",
        "Follow Muuse": "跟随鼠标",
        "Requires library reload or Blender restart": "需要重新加载资产库或重启 Blender",
        "Show Plug Count next to Library name": "显示库名称旁的零件数量",
        "Show Plugs 'In Front' when bringin them into the scene": '导入零件场景时 "在前" 显示零件',
        "Addon Terminal Registration Output": "插件终端注册输出",
        "Youtube Playlist, Pre-Release available on Patreon": "油管播放列表",
        "True":"开",
        "False":"关",
    },
    "zh_TW": {
        "Language": "語言",
        "Chat Room": "加入 QQ 學習頻道",
        "Buy More": "更新插件",
        "Language Description": "切換到中文界面時, 請在“編輯-偏好設置-界面-翻譯”裡取消勾選“新建數據”!!!",
        "Watch online video tutorials": "查看在線視頻教程",
        "Translation is in progress. Whole text isn't translated": "翻譯進行中, 優化糾錯請聯繫 iBlender®taobao.com",
        "This keymap should be the same as your native Blender Loop Select Keymapping": "此快捷鍵應與本地 Blender 循環選擇鍵相同",
        "If you don't use ALT + LMB for loop selecting, remap this accordingly!": "如果您不使用 ALT + 左鍵 進行循環選擇, 請相應地重新設定此快捷鍵！",
        "Mirror Vertex Groups": "鏡像頂點群組",
        "Addon": "外掛程式",
        "Print Addon Registration Output in System Console": "在系統控制台上列印插件註冊輸出結果",
        "Name of Matcap used for Surface Check.": "用於表面檢查的材質擷取名稱。",
        "Show in Blender's Edit Mesh Context Menu": "在 Blender 的編輯網格上下文選單中顯示",
        "Leave Empty, to disable": "留空以停用",
        "Show Mesh Split Tool": "顯示網格分割工具",
        "Show in Blender's Object Context Menu": "在 Blender 的物件上下文選單中顯示",
        "Follow Muuse": "跟隨滑鼠",
        "Requires library reload or Blender restart": "需要重新載入資產庫或重新啟動 Blender",
        "Show Plug Count next to Library name": "顯示庫名稱旁的零件數量",
        "Show Plugs 'In Front' when bringin them into the scene": '導入零件場景時 "在前" 顯示零件',
        "Addon Terminal Registration Output": "外掛程式終端註冊輸出",
        "Youtube Playlist, Pre-Release available on Patreon": "油管播放清單",
        "    • Force Deform: %s ": "• 強制形變: %s",
        "  • Force Deform: %s ": "• 強制形變: %s",
        "  • Type: %s": "• 類型: %s",
        "  • Use Deformer: %s": "• 使用形變器: %s",
        "(B) Unbevel": "(B) 反倒角",
        "(C) Unchamfer": "(C) 反平角",
        "(D) Unfuse": "(D) 反融合",
        "(E) Flatten": "(E) 展平",
        "(R) Refuse": "(R) 再融合",
        "(W) Change Width": "(W) 改變寬度",
        "(X) Delete Plug": "(X) 刪除零件",
        "(X) Delete Plugs": "(X) 刪除零件",
        "(X) Delete": "(X) 刪除",
        "(X) Unf*ck": "(X) Unf*ck",
        "(Y) Delete Plug": "(Y) 刪除零件",
        "(Y) Delete Plugs": "(Y) 刪除零件",
        "(Y) Split": "(Y) 拆分",
        "+ to -": "+ 到 -",
        "- to +": "- 到 +",
        "3D Cursor": "3D 游標",
        "A new version is available!": "有新版本可用!!",
        "ALT scroll  UP/DOWN": "Alt 上/下滾輪",
        "ALT scroll UP/DOWN": "Alt 上/下滾輪",
        "About": "關於",
        "Actions": "動作",
        "Add Boolean Modifier": "添加布爾修改器",
        "Add Boolean": "添加布爾",
        "Add Mode": "添加模式",
        "Add Plug to Library": "添加零件到庫",
        "Add a new empty library": "添加一個空的零件庫",
        "Add selected Plug to Plug Library": "增加選中的零件到零件庫",
        "Advanced Mode": "高級模式",
        "Advanced": "高級",
        "Align Mode": "對齊模式",
        "Align non-cyclic edge loop along bezier": "沿著貝塞爾曲線對齊非循環邊緣環",
        "All": "全部",
        "Along Edge": "沿著邊緣",
        "Along Normal": "沿著法線",
        "Alpha": "Alpha",
        "Also Clear Vertex Groups": "還清除頂點組",
        "Amount": "數量",
        "Angle Threshold Presets": "角度閾值預設",
        "Angle Threshold": "角度閾值",
        "Angle": "角度",
        "Appereance": "表現",
        "Apply All Modifiers": "應用所有修改器",
        "Apply Booleans": "應用布爾",
        "Apply Data Transfers": "應用數據傳輸",
        "Apply Mod": "應用修改器",
        "Apply Normal Transfer": "應用法線傳遞",
        "Apply Shrink Wrap": "應用縮裹",
        "Apply all Boolean Modifiers, and stash the Cutters": "應用所有布爾修改器, 並隱藏切割器",
        "Are you sure? This cannot be undone!": "你確定嗎？此操作無法撤銷!",
        "Asset Loaders": "資產導入器",
        "Auto-Smooth": "自動平滑",
        "Auto-X-Ray the plug and its subsets, when inserting Plug into scene": "場景插入零件時, 自動透視零件及其子集",
        "Average Tension": "平均張力",
        "Axis": "軸向",
        "BWeight": "倒角權重",
        "Basic": "基礎",
        "Best fit": "最佳適配",
        "Blender Addons": "Blender 插件",
        "Blender Market": "Blender Market",
        "Boolean Cleanup": "布爾清理",
        "Bridge": "橋",
        "CTRL scroll UP/DOWN": "Ctrl 滾輪上/下",
        "CURVEmachine": "CURVEmachine",
        "Cancel": "取消",
        "Cap Holes": "封孔",
        "Cap": "端點",
        "Careful, values above 4 are increasingly slow": "小心, 大於 4 會極慢",
        "Chamfer cyclic selections resulting from Boolean Operations": "由布爾運算產生的循環選擇面倒角",
        "Chamfer": "平角",
        "Change Width": "改變寬度",
        "Change the width of Chamfers(flat Bevels)": "改變倒角的寬度（平面斜角）",
        "Change this, so Plugs created by you, are tagged with your info!": "留下你的大名, 世界就是你的了!",
        "Circle": "圓環",
        "Clear Center Sharps": "清除中心銳邊",
        "Clear Existing Normals": "清除現有法線",
        "Clear Loop": "清除循環",
        "Clear Normals": "清除法線",
        "Clear Plug Props": "清除零件屬性",
        "Clear Plug properties": "清除零件屬性",
        "Clear library prefs, resets them into their original state.\nNo plugs will be lost!\nSave prefs and restart Blender to complete the process": "清除庫偏好設置, 將其重置為原始狀態。\n不會丟失任何插花！保存偏好設置並重啟Blender 以完成該過程",
        "Clear": "清除",
        "Color": "顏色",
        "Conform selection to Stash surface": "適配選中到隱藏物表面",
        "Conform": "貼合",
        "Contain": "包含",
        "Convert Mirrod Modifiers into real geometry with proper origins and properly mirrored custom normals": "將Mirrod Modifiers轉換為具有適當原點和正確鏡像的自定義法線的實際幾何體",
        "Convert a triangular Bevel Corner to Quad Corner": "將三邊形倒角轉角轉換為四邊形倒角轉角",
        "Corner": "夾角",
        "Couldn't find 2 quads on either side of the selection!": "在選擇的任一側都找不到 2 個四邊形!",
        "Countdown (s)": "倒數 (s)",
        "Create Plug from mesh object(s)": "從網格對象創建插入零件",
        "Create Plug": "創建零件",
        "Create RealMirror Collections": "創建真鏡像集合",
        "Create Stash": "創建暫存",
        "Create Vertex Group": "創建頂點組",
        "Create Vertex Groups": "創建頂點組",
        "Create Wedge from Edge Selection": "從邊選擇創建楔形",
        "Create a surface confirming polygon patch by drawing 4 corner points.": "通過繪製 4 個拐角點來創建確認多邊形面片的曲面。",
        "Create rounded Bevels from Chamfers": "從倒角創建圓倒角平倒角",
        "Creator: %s": "創建者: %s",
        "Cubic": "立方",
        "Custom Normal Mirror Method": "自定義法線鏡像法",
        "Custom Normal Pairing Method": "自定義法線配對方法",
        "Custom Radius": "自定義半徑",
        "Custom": "自定義",
        "Cyclic selections are not supported, aborting": "不支持循環選擇，中止",
        "Cyclic": "循環",
        "DECALmachine": "DECALmachine",
        "DEFORMER": "形變器",
        "Debug Whatever": "調試",
        "Debug": "調試",
        "Define Wedge Area": "定義楔形區域",
        "Define Wedge Depth": "定義楔形深度",
        "Deform Plug": "形變零件",
        "Deform Subsets": "形變子集",
        "Deformation": "形變",
        "Deformer Precision": "形變精度",
        "Deformer": "形變器",
        "Deformer: %s": "形變器: %s",
        "Delete All": "全部刪除",
        "Delete Handle, Plug and all Support Objects": "刪除控制柄, 零件和全部支持對象",
        "Delete": "刪除",
        "Deleting All": "全部刪除",
        "Deleting Marked": "刪除標記的",
        "Delta Matrix": "Delta 矩陣",
        "Depth Amount": "深度數量",
        "Difference": "差集",
        "Direction": "方向",
        "Display": "顯示",
        "Dissolve Angle": "消融角度",
        "Dissolve": "消融",
        "Docs": "官方文檔",
        "Documentation": "官方文檔",
        "Documention": "官方文檔",
        "Down": "下",
        "Draw Active Stash in 3D View": "在 3D 視圖中繪製活動暫存",
        "Draw Active Stash in X-Ray": "在透視中繪製活動暫存",
        "Draw Debug": "繪製調試",
        "Draw Properties": "繪製屬性",
        "Draw Timer": "顯示計時器",
        "Duplicate Boolean Objects with their Cutters\nALT: Instance the Object and Cutter Meshes": "使用切割器複製布爾對象\nALT：實例化對象和切割網格",
        "Duplicate Booleans": "複製布爾",
        "Edge Index": "邊索引",
        "Edge": "邊",
        "Edit Fillets by using Unfuse + Fuse in sequence": "(可以對已經成型的倒角再次編輯) Edit Fillets by using Unfuse + Fuse in sequence",
        "Edit": "編輯",
        "Editing %s": "編輯 %s",
        "Embed Plug into mesh surface": "將零件嵌入網格表面",
        "Empties found, but no ARRAY or HOOK modifiers present!": "發現空對象, 但無陣列或鉤掛修改器!",
        "Empty found, but no ARRAY or HOOK modifiers present!": "發現空對象, 但無陣列或鉤掛修改器!",
        "Empty: %s": "空: %s",
        "Exact": "精確",
        "Examples": "示例",
        "Experimental Features": "實驗功能",
        "Experimental": "實驗性",
        "Extend": "延伸",
        "Extra": "其它",
        "FAQ": "常見問題",
        "FILLET or EDGE": "圓角或邊",
        "Face Method": "面方法",
        "Face Mode": "面模式",
        "Face": "面",
        "Factor": "係數",
        "Fade": "漸隱",
        "Fading wire frames (experimental)": "褪色線框（實驗功能)",
        "Failed to rename library": "重命名庫失敗",
        "False": "關",
        "Fast": "快速",
        "Fillet or Edge": "圓角或邊",
        "Fillet": "圓角",
        "Finish and select Cutters": "完成並選擇切割",
        "Finish": "完成",
        "Fit inside": "適配內部",
        "Fix Center Method": "修復中心方法",
        "Fix Center Seam": "修復中心接縫",
        "Fix Midpoint": "修復中點",
        "Flatten Along": "展平沿著",
        "Flatten Polygon(s) along Edges or Normal": "沿著邊或法線展平多邊形面",
        "Flatten uneven shading on (mostly) ngons": "在多邊面上展平不均勻的著色",
        "Flatten": "展平",
        "Flick Distance": "波動距離",
        "Flick": "波動",
        "Flip Red to Green": "紅翻綠",
        "Flip Wedge Direction": "翻轉楔形方向",
        "Flip": "翻轉",
        "Flipped Normals": "翻轉法線",
        "Flipped": "翻轉",
        "Follow Mouse": "跟隨鼠標",
        "Force Projected Loop": "強制投射循環",
        "Force Projected Loops": "強制投射循環",
        "Force Subset Deform": "強制子集形變",  # iBlender
        "Fuse": "融合",
        "General": "常規",
        "Generate Log Files and Instructions for a Support Request.": "生成日誌文件和支持請求的說明.",
        "Generate new UUID": "新建通用唯一標識符",
        "Get Loops or Handles": "獲取循環或者控制柄",
        "Get More Plugs": "獲得更多零件",
        "HOOK or ARRAY": "鉤掛或陣列",
        "HUD Color": "小窗顏色",
        "HUD Font Color": "小窗字體顏色",
        "HUD Scale": "提示小窗大小",
        "HUD": "提示小窗",
        "Handle contains N-Gons!": "控制柄包含多邊形!",
        "Handle polygons are flipped!": "控制柄多邊形被翻轉!",
        "Handle": "控制柄",
        "Handle: %s": "控制柄: %s",
        "Handles": "控制柄",
        "Has Fillet": "圓角",
        "Have you already removed it manualy, while Blender was running?": "是否是你在 Blender 運行時手動刪除了它?",
        "Help": "幫助",
        "Hide Deformer and Occluder and Others": "隱藏形變和遮擋器等",
        "Illegal Selection": "非法選擇",
        "Index": "索引",
        "Influence": "影響",
        "Info": "提示",
        "Initial Run": "初始化運行",
        "Initialize": "初始化",
        "Input": "輸入",
        "Insert Plug": "插入零件",
        "Insert": "插入",
        "Instance": "實例",
        "Integration": "積分",
        "Interpolation Falloff (Surface Deform)": "插值衰減（表面形變)",
        "Interpolation": "插值",
        "Intersect": "相交",
        "Invalid Plug": "Plug 無效",
        "Invalid Selection": "選擇無效",
        "Iterations": "迭代",
        "Keymaps": "鍵位映射",
        "LMB to place, F1 to undo, F2 to redo": "鼠標左鍵: 放置, F1: 撤消, F2: 重做",
        "LOCKED": "鎖定",
        "LSelect": "循環選擇",
        "Leave empty, to disable": "留空以禁用",
        "Length": "長度",
        "Libraries": "庫",
        "Library path could not be found, reload libraries or restart Blender.": "未找到庫路徑, 請重新加載庫或重啟 Blender。",
        "Library": "庫",
        "Limit by Sharps": "按銳邊限制",
        "Limit to Selection": "限製到選擇",
        "Linear": "線性",
        "Linked": "已關聯",
        "Local": "局部",
        "Location": "位置",
        "Lock X": "鎖定 X",
        "Lock Y": "鎖定 Y",
        "Lock Z": "鎖定 Z",
        "Loop Select": "循環選擇",
        "Loop Slide ": "循環滑移 ",
        "Loop Slide": "環移",
        "Loop edges don't intersect.": "循環邊不相交。",
        "Loop": "循環",
        "LoopTools": "LoopTools",
        "LoopTools' Circle as a modal": "LoopTools 的圓環作為模態",
        "LoopTools's Relax as a modal": "LoopTools 的 鬆弛 作為模態",
        "Loops": "循環",
        "MACHIN3": "M3",
        "MACHIN3: Add Plug Library": "MACHIN3: 添加零件庫",
        "MACHIN3: Add Plug To Library": "MACHIN3: 添加零件到庫",
        "MACHIN3: Boolean Apply": "MACHIN3: 布爾應用",
        "MACHIN3: Boolean Cleanup": "MACHIN3: 布爾清理",
        "MACHIN3: Boolean Duplicate": "MACHIN3: 布爾複製",
        "MACHIN3: Boolean": "MACHIN3: 布爾",
        "MACHIN3: Call MESHmachine Menu": "MESHmachine 菜單",
        "MACHIN3: Chamfer": "MACHIN3: 平角",
        "MACHIN3: Change Width": "MACHIN3: 更改寬度",
        "MACHIN3: Clear Plug Libraries": "MACHIN3: 清除零件庫",
        "MACHIN3: Clear Plug Properties": "MACHIN3: 清除零件屬性",
        "MACHIN3: Conform": "MACHIN3: 貼合",
        "MACHIN3: Create Plug": "MACHIN3: 創建零件",
        "MACHIN3: Create Stash": "MACHIN3: 創建暫存",
        "MACHIN3: Debug HUD": "MACHIN3: 調試小窗",
        "MACHIN3: Debug MESHmachine": "MACHIN3: 調試 MESHmachine",
        "MACHIN3: Debug Whatever": "MACHIN3: 上帝調試",
        "MACHIN3: Delete Plug": "MACHIN3: 刪除零件",
        "MACHIN3: Draw Debug": "MACHIN3: 繪製調試",
        "MACHIN3: Draw Plug": "MACHIN3: 繪製零件",
        "MACHIN3: Draw RealMirror": "MACHIN3: 繪製真鏡像",
        "MACHIN3: Draw Stash": "MACHIN3: 繪製暫存",
        "MACHIN3: Draw Symmetrize": "MACHIN3: 繪製對稱",
        "MACHIN3: Draw Transferred Stashes": "MACHIN3: 繪製轉移暫存",
        "MACHIN3: Flatten": "MACHIN3: 展平",
        "MACHIN3: Fuse": "MACHIN3: 融合",
        "MACHIN3: Get Angle": "MACHIN3: 獲取角度",
        "MACHIN3: Get Faces Linked to Verts": "MACHIN3: 獲取與頂點相連的面",
        "MACHIN3: Get Length": "MACHIN3: 獲取長度",
        "MACHIN3: Get MESHmachine Support": "MACHIN3: 獲取 MESHmachine 支持",
        "MACHIN3: Get Sides": "MACHIN3: 獲得滑移",
        "MACHIN3: Loop Select": "MACHIN3: 循環選擇",
        "MACHIN3: LoopTools Circle": "MACHIN3: 循環工具圈環",
        "MACHIN3: LoopTools Relax": "MACHIN3: 循環工具鬆弛",
        "MACHIN3: Loops or Handles": "MACHIN3: 循環或控制柄",
        "MACHIN3: Make Unique": "MACHIN3: 唯一",
        "MACHIN3: Mark Loop": "MACHIN3: 標記循環邊",
        "MACHIN3: Move Plug Library": "MACHIN3: 移動零件庫",
        "MACHIN3: Normal Clear": "MACHIN3: 清除法線",
        "MACHIN3: Normal Flatten": "MACHIN3: 展平法線",
        "MACHIN3: Normal Straighten": "MACHIN3: 拉直傳遞",
        "MACHIN3: Normal Transfer": "MACHIN3: 傳遞法線",
        "MACHIN3: Offset Cut": "MACHIN3: 偏移切割",
        "MACHIN3: Offset": "MACHIN3: 偏移",
        "MACHIN3: Open Plug Library": "MACHIN3: 打開插花庫",
        "MACHIN3: Plug": "MACHIN3: 零件",
        "MACHIN3: Quad Corner": "MACHIN3: 四邊化夾角",
        "MACHIN3: Quick Patch": "MACHIN3: 快速面片",
        "MACHIN3: Real Mirror": "MACHIN3: 真鏡像",
        "MACHIN3: Refuse": "MACHIN3: 再融合",
        "MACHIN3: Reload Plug Libraries": "MACHIN3: 重載零件庫",
        "MACHIN3: Remove Stash": "MACHIN3: 刪除暫存",
        "MACHIN3: Select": "MACHIN3: 選擇",
        "MACHIN3: Set Plug Props": "MACHIN3: 設置零件屬性",
        "MACHIN3: Sharp Select": "MACHIN3: 銳邊選擇",
        "MACHIN3: Swap Stash": "MACHIN3: 交換暫存",
        "MACHIN3: Sweep Stashes": "MACHIN3: 清掃暫存",
        "MACHIN3: Symmetrize": "MACHIN3: 對稱",
        "MACHIN3: Transfer Stashes": "MACHIN3: 轉移暫存",
        "MACHIN3: Turn Corner": "MACHIN3: 轉向夾角",
        "MACHIN3: Unbevel": "MACHIN3: 反倒角",
        "MACHIN3: Unchamfer": "MACHIN3: 反平角",
        "MACHIN3: Unf*ck": "MACHIN3:反操蛋",
        "MACHIN3: Unfuse": "MACHIN3: 反融合",
        "MACHIN3: Validate Plug": "MACHIN3: 驗證零件",
        "MACHIN3: Vertex Group Select": "MACHIN3: 頂點組選擇",
        "MACHIN3: Vertex Info": "MACHIN3: 頂點信息",
        "MACHIN3: View Orphan Stashes": "MACHIN3: 查看孤零暫存",
        "MACHIN3: View Stashes": "MACHIN3: 查看暫存",
        "MACHIN3: Wedge": "MACHIN3: 楔形",
        "MACHIN3tools": "M3 工具",
        "MESHmachine Documentation": "MESHmachine 官方文檔",
        "MESHmachine Help": "MESHmachine 幫助",
        "MESHmachine": "MESHmachine",
        "MOVE LEFT/RIGHT, toggle W, reset ALT + W": "移動 左/右, 切換 W, 重置 Alt + W",
        "Make Instanced Mesh Objects Unique incl. any Instanced Boolean Operators": "使實例網格對象獨一無二, 包括任何實例化布爾操作",
        "Make Unique": "使唯一",
        "Make sure they aren't just ARRAY caps, that need to have their props cleared!": "確保它們不僅僅是陣列端點, 需要清理端點的屬性!",
        "Make sure to select a single, manifold edge, with at least one end leading to exactly 2 other edges!": "確保選擇有一端正好通向另外兩條邊的單一流形邊!",
        "Mapping": "映射",
        "Mark Loop": "標記循環邊",
        "Mark/Unmark edges for preferential treatement by Fuse/Refuse": "標記/取消標記邊以便熔接/反熔接進行優先處理",
        "MatCap Switch": "切換材質捕獲",
        "Matcap used for Surface Check.": "檢查表面的材質捕獲",
        "Menu": "菜單",
        "Merge Perimeter": "合併周長",
        "Merge verts on cyclic selections resulting from Boolean operations": "合併由布爾運算產生的循環選擇的頂點",
        "Merge": "合併",
        "Mesh Deform": "網格形變",
        "Method": "方法",
        "Min Angle": "最小角度",
        "Mirror Custom Normals": "鏡像自定義法線",
        "Modal": "模態",
        "Mode": "模式",
        "Mode: Symmstrize": "模式: 對稱",
        "Modifier: %s": "修改器: %s",
        "Mouse Pointer": "鼠標指針",
        "Mouse Position for Plug Insertion": "插入零件的鼠標位置",
        "Move library up or down.\nThis controls the position in the MESHmachine Plug Libraries submenu.\nSave prefs to remember": "上下移動庫。\n這控制MESHmachine 插花庫子菜單中的位置。\n調整後請保存偏好",
        "Multiple Creators": "多個創作者",
        "Multiple Deformers found!": "發現多個形變器!",
        "Multiple Empties": "多個空對象",
        "Multiple Empties:": "多個空對象: ",
        "Multiple Handles found!": "找到多個控制柄!",
        "Multiple Modifiers:": "多個修改器: ",
        "Multiple Occluders found!": "發現多個遮擋物!",
        "Multiple Others:": "多個其它: ",
        "Multiple Plug Meshes found!": "發現多個零件網格!",
        "Multiple Subsets": "多個子集",
        "Multiple Subsets:": "多個子集: ",
        "Multiple UUIDs": "多個通用唯一標識符",
        "My other Blender Addons": "我的其它插件",
        "Name (optiona)": "名稱（可選)",
        "Name (optional)": "名稱(可選)",
        "Nearest Corner and Best Matching Face Normal": "最近的夾角和最佳匹配面法線",
        "Nearest Corner and Best Matching Normal": "最近的夾角和最佳匹配法線",
        "Nearest Face Interpolated": "最近插值面",
        "Nearest Vertex": "最近頂點",
        "New Library Name": "新建庫名稱",
        "New Name": "新名稱",
        "New Name:": "新名稱:",
        "New": "新",
        "No Handle found!": "未發現控制柄!",
        "No Plug Mesh found!": "未發現零件網格!",
        "No active object in selection.": "選擇中無活動對象.",
        "No active object!": "無活動對象!",
        "No new name chosen.": "未選擇新名稱.",
        "Non-Manifold Geometry": "非流形幾何",
        "Non-manifold edges are part of the selection. Failed to determine sides of the selection.": "非流形邊緣是選擇的一部分. 無法確定選擇的邊.",
        "None": "無",
        "Normal Flatten": "展平法線",
        "Normal Transfer Matcap": "法線傳遞材質捕獲",
        "Normal Transfer": "法線傳遞",
        "Normals": "法線",
        "Not covered by Product Support!": "風險自負!",
        "Objet Mode": "對像模式",
        "Occluder": "遮擋",
        "Occluder: %s": "遮擋物: %s",
        "Offset Cut": "偏移切割",
        "Offset Patch": "面片偏移",
        "Offset cyclic edgeloops resulting from Boolean Operations to create a perimeter loop": "從布爾操作對像中的交叉部位循環邊偏移",
        "Offset": "偏移",
        "Old Name:": "舊名稱: ",
        "Online": "在線",
        "Only single-island cyclic or non-cyclic edge loop selections are supproted.": "僅支持單島循環或非循環邊循環選擇。",
        "Open selected library in file browser": "在文件瀏覽器裡打開選中的零件庫",
        "Optimize": "優化",
        "Orphan Matrix": "孤零矩陣",
        "Other object found, but no ARRAY modifiers present! What is it?": "發現其它對象, 但沒有陣列修改器預設! 它是啥啥東西?",
        "Other objects found, but no ARRAY modifiers present! What are they?": "發現其它對象, 但是沒有陣列修改器預設! 它是啥東西?",
        "Other: {}": "其它",
        "Parallel (all)": "平行 (全部)",
        "Partial": "部分",
        "Path Preview": "路徑預覽",
        "Pick Axis": "拾取軸向",
        "Planar Wedge Quad": "平面楔形四邊形",
        "Planar Wedge Quad: {}": "平面楔形四邊形: {}",
        "Plug Align": "對齊到:",
        "Plug Creator": "零件創造者",
        "Plug Libraries": "零件庫",
        "Plug Mode": "零件模式",
        "Plug Packs": "更多零件資產包",
        "Plug Precision": "零件精度",
        "Plug Resources": "零件資源",
        "Plug Settings": "零件設置",
        "Plug Utils": "零件工具箱",
        "Plug handle does not seem to have a plug object as a child": "Plug 控制柄似乎沒有做子級的零件對象",
        "Plug is missing a 'conform' vertex group.": "Plug 缺少“貼合”頂點組。",
        "Plug": "零件",
        "Plug: %s": "零件: %s",
        "Plugged": "官方範例",
        "Plugs can't just consist of a handle, they need to have a plug mesh as well.": "零件不能只有一個控制柄組成，它們還需要有一個零件網格.",
        "Plugs": "零件",
        "Points": "點",
        "Precision": "精度",
        "Project": "投射",
        "Projected Face Interpolated": "投射插值面",
        "Projected Loops": "投射循環",
        "Propagate": "傳遞",
        "Property": "屬性",
        "Quad Corner": "四邊化夾角",
        "Quick Patch": "快速面片",
        "Radius": "半徑",
        "Raycast": "光線投射",
        "Reach": "達到",
        "Real Mirror": "真鏡像",
        "Rebuild": "重構",
        "Reconstruct Chamfer from Fillet/rounded Bevel": "從圓形倒角重建倒角",
        "Reconstruct hard edge from Bevel by using Unfuse + Unchamfer in sequence": "通過依次使用Unfuse + Unchamfer從斜角重建硬邊",
        "Reconstruct hard edge from Chamfer": "重建倒角的硬邊",
        "Redefine Wedge Area": "重新定義楔形面積",
        "Redirect Chamfer Flow by turning a corner where 3 Chamfers meet": "通過轉動 3 個倒角相交的拐角來重定向反平倒角流",
        "Refuse": "再融合",
        "Regular": "常規",
        "Relax": "鬆弛",
        "Reload all libraries. Propagates forced lock settings.\nSave prefs to complete the process": "重載所有庫。強制鎖定設置。保存偏好設置以完成該過程",
        "Remove Plug Library": "移除零件庫",
        "Remove Plug": "移除零件",
        "Remove Plugs": "移除零件",
        "Remove Redundant Center": "移除冗餘中心",
        "Remove Sources": "移除源",
        "Remove VGroup": "移除頂點組",
        "Remove Vertex Group": "移除頂點組",
        "Remove selected plug library and all its plugs": "移除選中的零件庫及其所有零件",
        "Remove": "移除",
        "Removing a plug deletes it from the hard drive, this cannot be undone!": "從硬盤中刪除零件, 無法挽回!",
        "Rename Plug Library": "重命名零件庫",
        "Rename selected library": "重命名選中的零件庫",
        "Replace": "更換",
        "Resample": "重採樣",
        "Reset normals of the selected geometry, keep unselected geo as is": "在選中的幾何體上重置法線,沒選中的保持遠洋",
        "Retrieve and Re-Stash": "取回和重新暫存",
        "Retrieve": "取回",
        "Retrieved": "取回",
        "Reverse Plug Sorting (requires library reload or Blender restart)": "反向排序零件（需要重新加載庫或重啟 Blender)",
        "Reverse Plug Sorting": "反向排序零件",
        "Reverse": "反向",
        "Rotation": "旋轉",
        "Run VSelect, SSelect, LSelect or pass through to Blender": "運行頂點組選擇, 銳邊選擇, 循環選擇或傳遞到 Blender ",
        "SHIFT scroll UP/DOWN": "Shift 滾輪 上/下",
        "SSelect": "銳邊選擇",
        "Scale": "縮放",
        "Segments": "分段",
        "Select all sharp edges connected to the existing selection": "選擇連接到現有選擇的所有銳邊",
        "Select plug handle and a target object to plug into.": "選擇零件控制柄和目標對像以插入",
        "Select": "選擇",
        "Selected 2 faces are not next to each other!": "選定的 2 個面不相接!",
        "Selected only: {}": "僅選擇: {}",
        "Selected": "選定",
        "Selection Index": "選擇索引",
        "Selection does not have 3 corners, it's not a triangular corner, aborting": "選擇沒有 3 個夾角, 它不是三角夾角，中止",
        "Selection does not include faces, aborting": "選擇不包括面，中止",
        "Selection has less than 2 faces, aborting": "選擇的面少於 2 個，中止",
        "Selection has less than 3 verts selected, aborting": "選擇的頂點少於 3 個，正在中止",
        "Selection has less than 6 verts, aborting": "選擇少於 6 個頂點，中止",
        "Selection includes ngons, aborting": "選擇包括多邊形，中止",
        "Selection includes tris, aborting": "選擇包括三角面，中止",
        "Selection is cyclic, aborting": "選擇是循環，中止",
        "Selection is not a chamfer, aborting": "選擇非平角, 中止",
        "Selection is not a polysrip, aborting": "選擇非 polysrip, 中止",
        "Selection need to be at least 3 loop edges, aborting": "需要選擇至少 3 個循環邊，中止",
        "Selection": "選擇",
        "Self Stash": "自暫存",
        "Set BWeights": "設置倒角權重",
        "Set Bevel Weights": "設置倒角權重",
        "Set Cursor to Stash": "設置游標到暫存",
        "Set E/F": "設置 E/F",
        "Set Plug Props": "設置零件屬性",
        "Set Sharps": "設置銳邊",
        "Set/Change Plug properties": "設置/改變插入零件的屬性",
        "Shade Smooth": "平滑著色",
        "Shift": "切換",
        "Show Delete Menu": "顯示刪除菜單",
        "Show Hints": "顯示提示",
        "Show Indicators": "顯示指示",
        "Show LoopTools Wrappers": "顯示循環工具包裹",
        "Show Mesh Split tool": "顯示網格分割工具",
        "Show Plug Buttons below Libraries": "在庫下方顯示零件按鈕",
        "Show Plug Count next to Library Name": "在庫名稱旁邊顯示零件數",
        "Show Plug Name on Insert Button": "在內插按鈕上顯示零件名稱",
        "Show Plug Name on Insert Buttons": "在內插按鈕上顯示零件名稱",
        "Show Plug Names in Decal Libraries": "在貼花庫中顯示零件名稱",
        "Show Plug Names in Plug Libraries": "在零件庫中顯示零件名稱",
        "Show Plugs 'In Front' when bringing them into the scene": "導入零件到場景中時, 前置顯示",
        "Show Sidebar Panel": "顯示側邊欄面板",
        "Show Wire": "顯示線",
        "Show in Context Menus": "在上下文菜單中顯示",
        "Show in Edit Mode Context Menu": "在編輯模式上下文菜單中顯示",
        "Show in Object Mode Context Menu": "在對像模式上下文菜單中顯示",
        "Side A": "側 A",
        "Side B": "側 B",
        "Side Select": "側選",
        "Side": "側",
        "Simple": "簡單",
        "Single": "單",
        "Size of Icons in Plug Libaries": "零件庫中圖標的大小",
        "Size of Icons in Plug Libraries": "零件庫中圖標的大小",
        "Size of Plug Libary Icons": "零件庫圖標大小",
        "Size of Plug Library Icons": "零件庫圖標大小",
        "Slide Amount": "滑移數量",
        "Slide Wedge Corner: {}": "滑移楔形夾角: {}",
        "Slide": "滑移",
        "Smooth": "平滑",
        "Solver": "解算器",
        "Something went wrong, likely not a valid chamfer selection.": "不對? 選了非有效平角?",
        "Split": "分割",
        "Spread": "擴散",
        "Stash Index": "暫存索引",
        "Stash Matrix": "暫存矩陣",
        "Stash Object": "暫存對象",
        "Stash Operants": "暫存操作",
        "Stash Original": "暫存原版",
        "Stash it": "暫存它",
        "Stash the current state of an object": "隱藏物一個對象的當前狀態",
        "Stash the current state of the entire mesh or only the selected faces": "暫存整個網格或僅選定面的當前狀態",
        "Stash the current state of the object\nALT: Stash the evaluated mesh of the current object": "暫存對象當前狀態\nAlt: 暫存當前對象的評估網格",
        "Stash them": "暫存它們",
        "Stash": "暫存",
        "Stashes HUD Offset": "暫存小窗偏移",
        "Stashes HUD offset": "暫存小窗偏移",
        "Stashes Offset": "隱藏偏移",
        "Stashes: ": "暫存: ",
        "Steps": "步進",
        "Straighten uneven shading on straight fuse surface sections": "在選中的熔接面區域上拉直不均勻的著色(陰影怪異)",
        "Straighten": "拉直",
        "Subdivisions": "細分",
        "Subset Precision": "子集精度",
        "Subset": "子集",
        "Subset: %s": "子集: %s",
        "Subsets": "子集",
        "Surface Point": "曲麵點",
        "Swap": "交換",
        "Sweep Stashes": "清除暫存",
        "Sweep up stash objects, that became visible aber appending objects from other blend files": "清理隱藏對象, 這些對像在附加來自其它混合文件的對象時變得可見",
        "Symmetrize a mesh incl. its custom normals": "根據網格對象自身的自定義法線來對稱?",
        "Symmetrize": "對稱",
        "Tabs": "標籤",
        "Taper Flip": "翻轉錐化",
        "Taper": "錐化",
        "Target Matrix": "目標矩陣",
        "Target Normal": "目標法線",
        "Tension 2": "張力 2",
        "Tension Linked": "張力鏈接",
        "Tension Presets": "張力預設",
        "Tension": "張力",
        "The Imported Plug doesn't not contain a valid Plug Handle.": "導入零件不包含有效零件控制柄。",
        "The new name needs to be different from the old one.": "新名稱不能與舊名稱相同",
        "The selected object is not a (valid) plug handle, aborting": "所選對象非 (有效的) 零件控制柄, 中止",
        "There's a non-manifold edge closeby, failed to determine sides of the selection.": "附近有非流形邊, 無法確定選擇的邊",
        "This library exists already, choose another name!": "此庫已存在, 請選擇其它名稱!",
        "This removes the plug '%s' from library '%s'!": "這將刪除 “%s” 零件, 從庫 “%s” 中 !",
        "This removes the plug library '%s' and all its plugs!": "這將刪除插件庫 “%s” 及其所有零件!",
        "Threshold": "閾值",
        "Time (s": "時間（秒",
        "Time (s)": "時間 (秒)",
        "Time": "時間",
        "Timeout": "超時",
        "Tools": "工具",
        "Transfer Normals": "傳遞法線",
        "Transfer Stashes from one object to another": "對像到對象間傳遞隱藏對象",
        "Transfer Stashes": "傳遞暫存",
        "Transfer": "傳遞",
        "Transformation": "轉換",
        "Triangulate": "三角化",
        "True": "開",
        "Turn Corner": "轉向夾角",
        "Turn isolated edge preselections into loop selections with control overthe loop-angle": "通過控制循環角度, 將孤立邊預選轉變為循環選擇",
        "Turn them into Quad Corners first!": "請先把它們變成四邊夾角",
        "Turn": "轉換",
        "Tutorial": "教程",
        "UI": "用戶界面",
        "UUID: %s": "通用唯一標識符: %s",
        "UV Offset": "UV 偏移",
        "Unbevel": "反倒角",
        "Unchamfer Method": "反平角方法",
        "Unchamfer": "反平角",
        "Unf*ck": "Unf*ck",
        "Unfuse": "反融合",
        "Union": "並集",
        "Unsaved changes will be lost,": "將丟失未保存的更改, ",
        "Up": "上",
        "Update is available": "有更新可用",
        "Use Deformer": "使用形變器",
        "Use Experimental Features, at your own risk": "試驗功能, 送給喜歡吃螃蟹的",
        "Use Legacy Line Smoothing": "使用傳統線條平滑",
        "User Plug Libraries": "用戶零件庫",
        "User Plug Library Index": "用戶零件庫索引",
        "VIEW_3D": "視圖_3D",
        "VSelect": "頂點組選擇",
        "Validate Plug": "驗證零件",
        "Validate and Debug a Plug Asset": "驗證和測試插入零件",
        "Vert mode": "頂點模式",
        "Vertex Group": "頂點組",
        "View 3D": "3D 視圖",
        "View Oprhan Stashes": "顯示孤立隱藏物",
        "View Orphan Stashes": "查看孤零暫存",
        "View Stash": "查看暫存",
        "View Stashes": "查看暫存",
        "View stashes of an object and retrieve, edit or clear them": "查看對象的隱藏對象並檢索、編輯或清除它們",
        "Viewport": "視圖",
        "Visually select mesh elements by Vertex Group": "通過頂點組可視化選擇網格元素",
        "Web": "網絡",
        "Wedge": "楔形",
        "Weight": "權重",
        "Width (experimental)": "寬度（實驗功能)",
        "Width 2": "寬度 2",
        "Width Linked": "寬度",
        "Width": "寬度",
        "Wiggle": "扭動",
        "X-Ray": "投射",
        "You can't unfuse bevels with triangular coners": "無法反融合有三角化夾角的倒角",
        "Your current file is not saved!": "未保存當前文件!",
        "Youtube": "Youtube",
        "debug HUD": "調試 HUD",
        "from {} itself": "從 {} 自身",
        "from {}": "從 {}",
        "from {}'s selected faces": "從 {} 的選擇面",
        "has fillet": "圓角",
        "if you load the following example.": "如果您加載以下示例",
        "in Edit Mode": "在編輯模式",
        "in Object Mode": "在對像模式",
        "is plug deformer": "零件變形器",
        "is plug handle": "零件控制柄",
        "is plug occluder": "零件遮擋",
        "is plug subset": "零件子集",
        "is plug": "零件",
        "is stash object": "暫存對象",
        "move LEFT/RIGHT": "移動 左/右",
        "move LEFT/RIGHT, toggle W": "移動 左/右, 切換 W",
        "move LEFT/RIGHT, toggle W, reset ALT + W": "移動 左/右, 切換 W, 重置 Alt + W",
        "move LEFT/RIGHT, toggle W, reset ALT + W, presets Z/Y, X, C, V, B": "移動左/右, 切換W, 重置Alt + W, 預設Z/Y, X , C, V, B",
        "move LEFT/RIGHT, toggle W, rest ALT + W": "移動 左/右, 切換 W, 重置 ALT + W",
        "move UP/DOWN, toggle I, reset ALT + I": "移動 上/下, 切換 I, 重置 Alt + I",
        "move UP/DOWN, toggle T, presets Z/Y, X, C, V": "移動 上/下, 切換 T, 預設 Z/Y, X, C, V",
        "plug uuid": "零件通用唯一識別碼",
        "press A": "按 A",
        "press C": "按 C",
        "press D": "按 D",
        "press E": "按 E",
        "press R": "按 R",
        "press S": "按 S",
        "press SHIFT + ESC to finish": "按 Shift + ESC 結束",
        "reqiures library reload or Blender restart": "需要重新加載庫或重啟 Blender",
        "scroll UP/DOWN": "滾輪 上/下",
        "scroll UP/DOWN": "滾輪 上/下",
        "scroll UP/DOwn": "滾輪 上/下",
        "scroll UP/Down": "滾輪 上/下",
        "show in Context Menus": "顯示在上下文菜單中",
        "stash name": "暫存名稱",
        "stash uuid": "暫存通用唯一識別碼",
        "stash version": "暫存版本",
        "to %s": "到 %s",
        "toggle A": "切換 A",
        "toggle B": "切換 B",
        "toggle C": "切換 C",
        "toggle D": "切換 D",
        "toggle F": "切換 F",
        "toggle M": "切換 M",
        "toggle P": "切換 P",
        "toggle Q": "切換 Q",
        "toggle R": "切換 R",
        "toggle S": "切換 S",
        "toggle T": "切換 T",
        "toggle V": "切換 V",
        "toggle X": "切換 X",
        "Label": "標籤",
        "Object Mode": "物件模式",
        "Redundant Threshold Angle": "冗餘閾值角度",
        "Always Loop Select": "總是環選",
        "Edit Mode": "編輯模式",
        "Positive Z": "正 Z",
        "Negative X": "負 X",
        "FACE": "面",
        "Positive X": "正 X",
        "Apply Stashes Mods": "應用程式暫存修改器",
        "Apply All Mods Selected Objects": "套用所有修改器選取物件",
        "LOOP": "循環",
        "Apply Stash Mods": "應用暫存修改器",
        "No Boolean Mods found on Selected Objects": "在所選物件上未找到布林修改器",
        "FUSE": "融合",
        "LoopTools activation failed!": "啟用 LoopTools 失敗！",
    },
    "ja_JP": {
        "Language": "言語",
    },
    "fr_FR": {
        "Language": "Langue",
    },
    "de_DE": {
        "Language": "Sprache",
    },
    "ru_RU": {
        "Language": "Язык",
    },
    "ko_KR": {
        "Language": "언어",
    },
}


def translations_dict():
    d = {}
    local_language = bpy.context.preferences.view.language
    d[local_language] = {}

    if local_language in ["zh_HANS", "zh_CN"]:
        for context in bpy.app.translations.contexts:
            for k, v in ztranslate.get("zh_CN").items():
                d[local_language][(context, k)] = v
    elif local_language in ["zh_HANT", "zh_TW"]:
        for context in bpy.app.translations.contexts:
            for k, v in ztranslate.get("zh_TW").items():
                d[local_language][(context, k)] = v
    return d
