<!DOCTYPE html>
<html class="writer-html5" lang="en" >
<head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><link rel="canonical" href="https://machin3.io/MESHmachine/docs/index.html" />
      <link rel="shortcut icon" href="img/favicon.ico" />
    <title>MESHmachine - MESHmachine</title>
    <link rel="stylesheet" href="css/theme.css" />
    <link rel="stylesheet" href="css/theme_extra.css" />
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.5.0/styles/github.min.css" />
        <link href="css/extra.css" rel="stylesheet" />
    
      <script>
        // Current page data
        var mkdocs_page_name = "MESHmachine";
        var mkdocs_page_input_path = "index.md";
        var mkdocs_page_url = "/MESHmachine/docs/index.html";
      </script>
    
    <script src="js/jquery-3.6.0.min.js" defer></script>
    <!--[if lt IE 9]>
      <script src="js/html5shiv.min.js"></script>
    <![endif]-->
      <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.5.0/highlight.min.js"></script>
      <script>hljs.initHighlightingOnLoad();</script> 
</head>

<body class="wy-body-for-nav" role="document">

  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side stickynav">
    <div class="wy-side-scroll">
      <div class="wy-side-nav-search">
          <a href="." class="icon icon-home"> MESHmachine
        </a><div role="search">
  <form id ="rtd-search-form" class="wy-form" action="./search.html" method="get">
      <input type="text" name="q" placeholder="Search docs" title="Type search term here" />
  </form>
</div>
      </div>

      <div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="Navigation menu">
              <p class="caption"><span class="caption-text">Get Started</span></p>
              <ul class="current">
                  <li class="toctree-l1 current"><a class="reference internal current" href="index.html">MESHmachine</a>
    <ul class="current">
    <li class="toctree-l2"><a class="reference internal" href="#introduction">Introduction</a>
    </li>
    <li class="toctree-l2"><a class="reference internal" href="#support">Support</a>
    </li>
    <li class="toctree-l2"><a class="reference internal" href="#resources">Resources</a>
    </li>
    <li class="toctree-l2"><a class="reference internal" href="#features">Features</a>
    </li>
    <li class="toctree-l2"><a class="reference internal" href="#overview">Overview</a>
        <ul>
    <li class="toctree-l3"><a class="reference internal" href="#chamfers">Chamfers</a>
    </li>
    <li class="toctree-l3"><a class="reference internal" href="#fillets">Fillets</a>
    </li>
    <li class="toctree-l3"><a class="reference internal" href="#fuse">Fuse</a>
    </li>
    <li class="toctree-l3"><a class="reference internal" href="#plugs">Plugs</a>
    </li>
    <li class="toctree-l3"><a class="reference internal" href="#stashes">Stashes</a>
    </li>
    <li class="toctree-l3"><a class="reference internal" href="#booleans">Booleans</a>
    </li>
    <li class="toctree-l3"><a class="reference internal" href="#normal-tools">Normal Tools</a>
    </li>
    <li class="toctree-l3"><a class="reference internal" href="#mirroring">Mirroring</a>
    </li>
    <li class="toctree-l3"><a class="reference internal" href="#selection">Selection</a>
    </li>
    <li class="toctree-l3"><a class="reference internal" href="#looptools">Looptools</a>
    </li>
        </ul>
    </li>
    </ul>
                  </li>
                  <li class="toctree-l1"><a class="reference internal" href="installation.html">Installation</a>
                  </li>
                  <li class="toctree-l1"><a class="reference internal" href="preferences.html">Preferences</a>
                  </li>
                  <li class="toctree-l1"><a class="reference internal" href="whatsnew.html">What's new?</a>
                  </li>
                  <li class="toctree-l1"><a class="reference internal" href="faq.html">FAQ</a>
                  </li>
              </ul>
              <p class="caption"><span class="caption-text">Chamfers and Fillets</span></p>
              <ul>
                  <li class="toctree-l1"><a class="reference internal" href="change_width.html">Change Width</a>
                  </li>
                  <li class="toctree-l1"><a class="reference internal" href="fuse.html">Fuse</a>
                  </li>
                  <li class="toctree-l1"><a class="reference internal" href="flatten.html">Flatten</a>
                  </li>
                  <li class="toctree-l1"><a class="reference internal" href="unfuse.html">Unfuse</a>
                  </li>
                  <li class="toctree-l1"><a class="reference internal" href="refuse.html">Refuse</a>
                  </li>
                  <li class="toctree-l1"><a class="reference internal" href="unchamfer.html">Unchamfer</a>
                  </li>
                  <li class="toctree-l1"><a class="reference internal" href="unbevel.html">Unbevel</a>
                  </li>
                  <li class="toctree-l1"><a class="reference internal" href="unfuck.html">Unf*ck</a>
                  </li>
                  <li class="toctree-l1"><a class="reference internal" href="turn_corner.html">Turn Corner</a>
                  </li>
                  <li class="toctree-l1"><a class="reference internal" href="quad_corner.html">Quad Corner</a>
                  </li>
                  <li class="toctree-l1"><a class="reference internal" href="mark_loop.html">Mark Loop</a>
                  </li>
              </ul>
              <p class="caption"><span class="caption-text">Plugs</span></p>
              <ul>
                  <li class="toctree-l1"><a class="reference internal" href="plugs_introduction.html">Plugs Introduction</a>
                  </li>
                  <li class="toctree-l1"><a class="reference internal" href="plug_creation.html">Plug Creation</a>
                  </li>
                  <li class="toctree-l1"><a class="reference internal" href="advanced_plug_creation.html">Advanced Plug Creation</a>
                  </li>
                  <li class="toctree-l1"><a class="reference internal" href="remove_plugs.html">Remove Plugs</a>
                  </li>
                  <li class="toctree-l1"><a class="reference internal" href="delete_plug.html">Delete Plug</a>
                  </li>
                  <li class="toctree-l1"><a class="reference internal" href="plug_resources.html">Plug Resources</a>
                  </li>
              </ul>
              <p class="caption"><span class="caption-text">Booleans</span></p>
              <ul>
                  <li class="toctree-l1"><a class="reference internal" href="boolean.html">Boolean</a>
                  </li>
                  <li class="toctree-l1"><a class="reference internal" href="boolean_cleanup.html">Boolean Cleanup</a>
                  </li>
                  <li class="toctree-l1"><a class="reference internal" href="chamfer.html">Chamfer</a>
                  </li>
                  <li class="toctree-l1"><a class="reference internal" href="offset.html">Offset</a>
                  </li>
              </ul>
              <p class="caption"><span class="caption-text">Stashes</span></p>
              <ul>
                  <li class="toctree-l1"><a class="reference internal" href="create_stash.html">Create Stash</a>
                  </li>
                  <li class="toctree-l1"><a class="reference internal" href="view_stashes.html">View Stashes</a>
                  </li>
                  <li class="toctree-l1"><a class="reference internal" href="transfer_stashes.html">Transfer Stashes</a>
                  </li>
                  <li class="toctree-l1"><a class="reference internal" href="stashes_panel.html">Stashes Panel</a>
                  </li>
                  <li class="toctree-l1"><a class="reference internal" href="orphan_stashes.html">Orphan Stashes</a>
                  </li>
                  <li class="toctree-l1"><a class="reference internal" href="sweep_stashes.html">Sweep Stashes</a>
                  </li>
                  <li class="toctree-l1"><a class="reference internal" href="conform.html">Conform</a>
                  </li>
                  <li class="toctree-l1"><a class="reference internal" href="quick_patch.html">Quick Patch</a>
                  </li>
              </ul>
              <p class="caption"><span class="caption-text">Normals</span></p>
              <ul>
                  <li class="toctree-l1"><a class="reference internal" href="normal_flatten_straighten.html">Normal Flatten and Straighten</a>
                  </li>
                  <li class="toctree-l1"><a class="reference internal" href="normal_transfer.html">Normal Transfer</a>
                  </li>
                  <li class="toctree-l1"><a class="reference internal" href="normal_clear.html">Normal Clear</a>
                  </li>
              </ul>
              <p class="caption"><span class="caption-text">Mirror</span></p>
              <ul>
                  <li class="toctree-l1"><a class="reference internal" href="symmetrize.html">Symmetrize</a>
                  </li>
                  <li class="toctree-l1"><a class="reference internal" href="real_mirror.html">Real Mirror</a>
                  </li>
              </ul>
              <p class="caption"><span class="caption-text">Selection</span></p>
              <ul>
                  <li class="toctree-l1"><a class="reference internal" href="lselect.html">LSelect</a>
                  </li>
                  <li class="toctree-l1"><a class="reference internal" href="sselect.html">SSelect</a>
                  </li>
                  <li class="toctree-l1"><a class="reference internal" href="vselect.html">VSelect</a>
                  </li>
                  <li class="toctree-l1"><a class="reference internal" href="select.html">Select</a>
                  </li>
              </ul>
              <p class="caption"><span class="caption-text">Refinement</span></p>
              <ul>
                  <li class="toctree-l1"><a class="reference internal" href="wedge.html">Wedge</a>
                  </li>
              </ul>
              <p class="caption"><span class="caption-text">Looptools</span></p>
              <ul>
                  <li class="toctree-l1"><a class="reference internal" href="circle_relax.html">Circle and Relax</a>
                  </li>
              </ul>
              <ul>
                <li class="toctree-l1"><a class="reference internal" href="changelog.html">Changelog</a>
                </li>
              </ul>
      </div>
    </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap">
      <nav class="wy-nav-top" role="navigation" aria-label="Mobile navigation menu">
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href=".">MESHmachine</a>
        
      </nav>
      <div class="wy-nav-content">
        <div class="rst-content"><div role="navigation" aria-label="breadcrumbs navigation">
  <ul class="wy-breadcrumbs">
    <li><a href="." class="icon icon-home" alt="Docs"></a> &raquo;</li>
          <li>Get Started &raquo;</li>
      <li>MESHmachine</li>
    <li class="wy-breadcrumbs-aside">
    </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
            <div class="section" itemprop="articleBody">
              
                <h1 id="_1"></h1>
<h2 id="introduction">Introduction</h2>
<p><a href="https://gumroad.com/a/164689011/Gwgt"><strong>Gumroad</strong></a> | <a href="https://www.blendermarket.com/products/MESHmachine?ref=1051"><strong>Blender Market</strong></a> - <a href="https://blenderartists.org/t/meshmachine/1102529/">Blender Artists</a> | <a href="https://polycount.com/discussion/205933/blender-meshmachine-hard-surface-focused-mesh-modeling">Polycount</a> - <a href="https://www.youtube.com/c/MACHIN3/videos">Youtube</a> - <a href="https://twitter.com/machin3io">Twitter</a> - <a href="https://patreon.com/machin3">Patreon</a> - <a href="mailto:<EMAIL>">eMail</a></p>
<p><img alt="MESHmachine0.16" src="img/hero.jpg" />
<em>cover image based on <a href="https://www.artstation.com/artwork/nQWPo">Humanoid Mecha</a> concept by <a href="https://www.artstation.com/inkertone">Bruno Gauthier Leblanc</a></em></p>
<div style="position: relative; width: 100%; height: 0; padding-bottom: 56.25%;">
<iframe src="https://www.youtube.com/embed/5hvusH1QrRc" style="position: absolute; top: 0; left: 0; width: 100%; height: 100%;" allowfullscreen seamless frameBorder="0"></iframe>
</div>

<hr />
<p><strong>MESHmachine</strong> is a <em>blender mesh modeling addon</em> with a focus on hard surface work without subdivision surfaces.  </p>
<p>MESHmachine's <em>chamfer and fillet</em> toolset allows for more flexibility, when dealing with fillet-like surfaces, traditionally created with the <em>Bevel</em> and <em>Bridge</em> tools.<br />
MESHmachine's approach to fillets is the <a href="fuse.html">Fuse</a> tool, which builds transitional surfaces from chamfers, <em>fusing</em> the surfaces on both sides.<br />
Doing this, you get the benefits of both - chamfers and fillets - while avoiding their disadvantages.</p>
<p>Read on below for an <a href="#overview">overview</a> of MESHmachine's tools and ideas.</p>
<h2 id="support">Support</h2>
<div class="admonition danger">
<p class="admonition-title">Attention</p>
<p>If you need to get in touch with me to <strong>report an error</strong>, <strong>report tool misbehavior</strong> or <strong>have another problem</strong> <a href="faq.html#get-support"><strong>READ THIS FIRST</strong></a>.</p>
</div>
<h2 id="resources">Resources</h2>
<p>For <strong>installation</strong> help, see the <a href="installation.html">Installation</a> page.<br />
For <strong>questions</strong> you have or problems you may run into, see the <a href="faq.html">FAQ</a>.<br />
To <strong>discuss</strong> using MESHmachine, please use the <a href="https://blenderartists.org/forum/showthread.php?448777-Addon-MESHmachine">blender artists</a> and <a href="https://polycount.com/discussion/205933/blender-meshmachine-hard-surface-focused-mesh-modeling#latest">polycount</a> threads.<br />
For <strong>plug creation</strong>, jump to <a href="plug_creation.html">Plug Creation</a>.<br />
To <strong>learn</strong> about all of <strong>MESHmachine's features and tools</strong>, you can either check out <a href="https://www.youtube.com/watch?v=i68jOGMEUV8&amp;list=PLcEiZ9GDvSdXR9kd4O6cdQN_6i0LOe5lw&amp;index=1">this playlist</a> on youtube, or better - access those same videos, but in context and with additional information through the sidebar to the left. </p>
<h2 id="features">Features</h2>
<ul>
<li>turn <a href="fuse.html">chamfers into fillets/bevels</a> and <a href="unfuse.html">back</a></li>
<li>change the width of a <a href="change_width.html">chamfer</a> or <a href="refuse.html">bevel</a></li>
<li>create <a href="fuse.html">variable fillets and washouts</a> </li>
<li><a href="unchamfer.html">unchamfer</a> and <a href="unbevel.html">unbevel</a> to go back to a hard edge</li>
<li>practically <a href="refuse.html">edit</a> existing existing bevels</li>
<li><a href="unfuck.html">resolve tricky geometry overlaps</a> in cases where two bevels meet</li>
<li><a href="flatten.html">flatten</a> multiple polygons based on another polygon or flatten a single polygon based on 3 vertices</li>
<li><a href="flatten.html">flatten</a> along a normal or flatten along edges</li>
<li>redirect chamfer flow by <a href="turn_corner.html">turning the corners</a></li>
<li>convert triangular bevel corners into <a href="quad_corner.html">quad corners</a></li>
<li><a href="plugs_introduction.html">plug details</a> into your mesh</li>
<li><a href="plug_creation.html">build your own plugs</a> and create/buy/sell plug libraries</li>
<li><a href="create_stash.html">stash</a> objects or face selections, creating backups, that can be <a href="view_stashes.html">brought back</a> or referenced by other tools, without cluttering the scene</li>
<li>conveniently <a href="boolean.html">add boolean modifiers</a> (incl. split booleans), create automatic stashes when applying the mods, and easily <a href="boolean.html#duplicate">duplicate or instance</a> objects using booleans</li>
<li><a href="boolean_cleanup.html">cleanup applied booleans</a> and create clean perimeter loops around their intersections</li>
<li>create <a href="normal_flatten_straighten.html">flattened, straightend</a>, and <a href="normal_transfer.html">transferred</a> custom normals</li>
<li><a href="symmetrize.html">symmetrize</a> meshes including their custom normals</li>
<li>turn mirror modifiers into <a href="real_mirror.html">real objects</a></li>
<li><a href="lselect.html">loop select</a>, <a href="sselect.html">sharp select</a> and <a href="vselect.html">vertex group select</a>, all <a href="select.html">using a single keymap</a></li>
<li>easily add <a href="wedge.html">wedges</a></li>
<li>best documentation in the business</li>
</ul>
<h2 id="overview">Overview</h2>
<h3 id="chamfers">Chamfers</h3>
<p><a href="faq.html#what-is-a-chamfer-what-is-a-fillet-what-is-a-bevel">Chamfers (flat bevels)</a> are easy to create, easy to manipulate and are easy to (loop) select.<br />
They are very useful to quickly block out more complex forms and already contain all the important information in regards to the flow of surfaces and edges as well as the chamfer width.</p>
<p>Chamfers are also a dead end, if you want to further refine them later on, unless you get down to the edge or vertex level, which is time consuming.<br />
Traditionally, they can't be easily turned into fillets(rounded bevels) and removing the chamfer polygons to bridge the open edge loops, bedides being tedious, often produces results without good surface continuity or with unnessesary edges.</p>
<p><img alt="BridgeBevelIssues" src="img/bridge_bevel_issues.jpg" />
<em>The overshooting is relatively easy to fix, </em><em>IF</em><em> you actually want to create a chamfer, but gets harder (support edges), if you want a rounded bevel.
So, what if you could turn a chamfer into a bevel?</em></p>
<h3 id="fillets">Fillets</h3>
<p><a href="faq.html#what-is-a-chamfer-what-is-a-fillet-what-is-a-bevel">Fillets (round bevels)</a> are also easy to create, using Blender's Bevel tool. But once created, they are hard or downright impossible to manipulate. Depending on the density, they are also significantly harder to select.<br />
Fillets are excellent for refined, finished forms however, but due to how hard they are to manipulate, using them early on usually means you are locking the design down. Changing it later becomes so hard, you're likely to deny yourself that option completely.<br />
As a result your design may suffer.</p>
<h3 id="fuse">Fuse</h3>
<p>Using MESHmachine's <a href="fuse.html">Fuse</a> and <a href="unfuse.html">Unfuse</a> tools, you can move between chamfers and fillets back and forth effortlessly - you can turn a chamfer into a fillet and a fillet back into a chamfer.<br />
This allows for significant gains in flexibility, because - remember - a chamfer is trivial to manipulate and once you've done so, you can turn it into a Fillet again.  </p>
<p>Moreover, the width of a chamfer will directly determine the width of a fillet(the radius if you want, but it's not precisly circular and depends on the tension setting).<br />
This in turn allows for the creation of variable fillets and even washouts, which are impossible to do with the bevel tool and modifier.</p>
<p><img alt="GifChangeDesign" src="img/change_design.gif" />
<em>tasks like these are usually pretty tedious, but are rather trivial with MESHmachine</em></p>
<h3 id="plugs">Plugs</h3>
<p><img alt="Plugs" src="img/plugs.jpg" /></p>
<p><a href="plugs_introduction.html">Plugs</a> are mesh inserts, that in combination with normal transfers, can produce flawless detailing, even on curved surfaces and even on medium or low density models.
MESHmachine provides tools to manage plug libraries and tools to create your own plugs well.</p>
<h3 id="stashes">Stashes</h3>
<p>MESHmachine introduced the concept of <a href="create_stash.html">Stashes</a>, which are basically object backups, that can also be referenced by other tools, like <a href="conform.html">Conform</a>.  </p>
<p>What's special about them, is the out-of-your-way approach, which means they won't clutter the scene and instead are accessed, edited, swapped or retrieved using a <a href="view_stashes.html">modal stash viewer</a> or the <a href="stashes_panel.html">panel</a> in the sidebar.</p>
<h3 id="booleans">Booleans</h3>
<p>MESHmachine provides tools to conveniently set up boolean operations in object mode using Blender's boolean modifier. 
It adds a 4th boolean <em>split</em> mode and allows for easy duplication or instancing of objects carrying boolean modifiers including their cutter objects.  </p>
<p>In addition it facilitates applying these modifiers and creates <a href="create_stash.html">stashes</a> from the boolean operants automatically.</p>
<p>Beyond that, MESHmachine supplies a few tools, that are helpful after boolean operations.<br />
<a href="boolean_cleanup.html">Boolean Cleanup</a> merges vertices on the transitional edge between two boolean operators.  </p>
<p>The <a href="chamfer.html">Chamfer</a> and <a href="offset.html">Offset</a> tools help to create clean boundary topology, which in turn is beneficial for tools like Bevel, <a href="change_width.html">Change Width</a> and <a href="fuse.html">Fuse</a>/<a href="refuse.html">Refuse</a>.</p>
<h3 id="normal-tools">Normal Tools</h3>
<p>In addition to modeling tools, MESHmachine has so far added 4 tools to manipulate vertex normals.<br />
Traditionally used in game art, if at all, this makes custom normals accessible in medium and high resolution mesh modeling as well and further loosens topology constraints in non-subd modeling.  </p>
<p>The <a href="normal_transfer.html">Normal Transfer</a> tool in combination with stashes, mirrors behavior of parametric solids and nurbs applicatinos and can produce the similar quality in terms of shading.</p>
<h3 id="mirroring">Mirroring</h3>
<p>Utilizing custom normals, requires additional tooling on the mirroring front, as Blender's symmetrize tool does not support custom normals.<br />
MESHmachine's <a href="symmetrize.html">Symmetrize</a> adds those missing normal mirror capabilities, and allows for efficient single-keymap "flick symmetrizing" all all 6 object space directions.</p>
<p>As for the mirror modifier, MESHmachine's <a href="real_mirror.html">Real Mirror</a> tool can create real geometry with properly mirrored object origins and custom normals from objects carrying mirror mods.
This then enables correct object-space texturing for mirrored geometry.  </p>
<h3 id="selection">Selection</h3>
<p>Loop selecting in context of non-subd or ngon modelling can be a challenge, because Blender's native loop select tool is aimed at quad topology.<br />
That's why MESHmachine introduces an angle-based loop select tool called <a href="lselect.html">LSelect</a>, that is free of quad topology constraints.</p>
<p>In addition, <a href="sselect.html">SSelect</a> allows for easy selections of connected sharp edges.</p>
<p><a href="vselect.html">VSelect</a> provides a quick, visual way to select geometry stored in vertex groups. This is especially useful in combination with Plugs and Normal Transfers, but can also be handy for rigging/skinning.</p>
<p>Tying these three as well as Blender's native loop select tool together, is the <a href="select.html">Select</a> tool, which is a wrapper around all 4, thereby enabling conditional use of each one via a single keymap.</p>
<h3 id="looptools">Looptools</h3>
<p>Looptools is a favourite addon of many blender users and ships with blender's default set of addons.<br />
MESHmachine <a href="preferences.html">provides modal wrappers</a>, so Looptools' <a href="circle_relax.html">Circle and Relax</a> can be used in the same fashion as MESHmachine's tools.</p>
              
            </div>
          </div><footer>
    <div class="rst-footer-buttons" role="navigation" aria-label="Footer Navigation">
        <a href="installation.html" class="btn btn-neutral float-right" title="Installation">Next <span class="icon icon-circle-arrow-right"></span></a>
    </div>

  <hr/>

  <div role="contentinfo">
    <!-- Copyright etc -->
  </div>

  Built with <a href="https://www.mkdocs.org/">MkDocs</a> using a <a href="https://github.com/readthedocs/sphinx_rtd_theme">theme</a> provided by <a href="https://readthedocs.org">Read the Docs</a>.
</footer>
          
        </div>
      </div>

    </section>

  </div>

  <div class="rst-versions" role="note" aria-label="Versions">
  <span class="rst-current-version" data-toggle="rst-current-version">
    
    
    
      <span><a href="installation.html" style="color: #fcfcfc">Next &raquo;</a></span>
    
  </span>
</div>
    <script>var base_url = '.';</script>
    <script src="js/theme_extra.js" defer></script>
    <script src="js/theme.js" defer></script>
      <script src="search/main.js" defer></script>
    <script defer>
        window.onload = function () {
            SphinxRtdTheme.Navigation.enable(true);
        };
    </script>

</body>
</html>
