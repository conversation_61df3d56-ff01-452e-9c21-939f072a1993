<!DOCTYPE html>
<html class="writer-html5" lang="en" >
<head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
      <link rel="shortcut icon" href="/MESHmachine/docs/img/favicon.ico" />
    <title>MESHmachine</title>
    <link rel="stylesheet" href="/MESHmachine/docs/css/theme.css" />
    <link rel="stylesheet" href="/MESHmachine/docs/css/theme_extra.css" />
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.5.0/styles/github.min.css" />
        <link href="/MESHmachine/docs/css/extra.css" rel="stylesheet" />
    
    <script src="/MESHmachine/docs/js/jquery-3.6.0.min.js" defer></script>
    <!--[if lt IE 9]>
      <script src="/MESHmachine/docs/js/html5shiv.min.js"></script>
    <![endif]-->
      <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.5.0/highlight.min.js"></script>
      <script>hljs.initHighlightingOnLoad();</script> 
</head>

<body class="wy-body-for-nav" role="document">

  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side stickynav">
    <div class="wy-side-scroll">
      <div class="wy-side-nav-search">
          <a href="/MESHmachine/docs/." class="icon icon-home"> MESHmachine
        </a><div role="search">
  <form id ="rtd-search-form" class="wy-form" action="/MESHmachine/docs//search.html" method="get">
      <input type="text" name="q" placeholder="Search docs" title="Type search term here" />
  </form>
</div>
      </div>

      <div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="Navigation menu">
              <p class="caption"><span class="caption-text">Get Started</span></p>
              <ul>
                  <li class="toctree-l1"><a class="reference internal" href="/MESHmachine/docs/index.html">MESHmachine</a>
                  </li>
                  <li class="toctree-l1"><a class="reference internal" href="/MESHmachine/docs/installation.html">Installation</a>
                  </li>
                  <li class="toctree-l1"><a class="reference internal" href="/MESHmachine/docs/preferences.html">Preferences</a>
                  </li>
                  <li class="toctree-l1"><a class="reference internal" href="/MESHmachine/docs/whatsnew.html">What's new?</a>
                  </li>
                  <li class="toctree-l1"><a class="reference internal" href="/MESHmachine/docs/faq.html">FAQ</a>
                  </li>
              </ul>
              <p class="caption"><span class="caption-text">Chamfers and Fillets</span></p>
              <ul>
                  <li class="toctree-l1"><a class="reference internal" href="/MESHmachine/docs/change_width.html">Change Width</a>
                  </li>
                  <li class="toctree-l1"><a class="reference internal" href="/MESHmachine/docs/fuse.html">Fuse</a>
                  </li>
                  <li class="toctree-l1"><a class="reference internal" href="/MESHmachine/docs/flatten.html">Flatten</a>
                  </li>
                  <li class="toctree-l1"><a class="reference internal" href="/MESHmachine/docs/unfuse.html">Unfuse</a>
                  </li>
                  <li class="toctree-l1"><a class="reference internal" href="/MESHmachine/docs/refuse.html">Refuse</a>
                  </li>
                  <li class="toctree-l1"><a class="reference internal" href="/MESHmachine/docs/unchamfer.html">Unchamfer</a>
                  </li>
                  <li class="toctree-l1"><a class="reference internal" href="/MESHmachine/docs/unbevel.html">Unbevel</a>
                  </li>
                  <li class="toctree-l1"><a class="reference internal" href="/MESHmachine/docs/unfuck.html">Unf*ck</a>
                  </li>
                  <li class="toctree-l1"><a class="reference internal" href="/MESHmachine/docs/turn_corner.html">Turn Corner</a>
                  </li>
                  <li class="toctree-l1"><a class="reference internal" href="/MESHmachine/docs/quad_corner.html">Quad Corner</a>
                  </li>
                  <li class="toctree-l1"><a class="reference internal" href="/MESHmachine/docs/mark_loop.html">Mark Loop</a>
                  </li>
              </ul>
              <p class="caption"><span class="caption-text">Plugs</span></p>
              <ul>
                  <li class="toctree-l1"><a class="reference internal" href="/MESHmachine/docs/plugs_introduction.html">Plugs Introduction</a>
                  </li>
                  <li class="toctree-l1"><a class="reference internal" href="/MESHmachine/docs/plug_creation.html">Plug Creation</a>
                  </li>
                  <li class="toctree-l1"><a class="reference internal" href="/MESHmachine/docs/advanced_plug_creation.html">Advanced Plug Creation</a>
                  </li>
                  <li class="toctree-l1"><a class="reference internal" href="/MESHmachine/docs/remove_plugs.html">Remove Plugs</a>
                  </li>
                  <li class="toctree-l1"><a class="reference internal" href="/MESHmachine/docs/delete_plug.html">Delete Plug</a>
                  </li>
                  <li class="toctree-l1"><a class="reference internal" href="/MESHmachine/docs/plug_resources.html">Plug Resources</a>
                  </li>
              </ul>
              <p class="caption"><span class="caption-text">Booleans</span></p>
              <ul>
                  <li class="toctree-l1"><a class="reference internal" href="/MESHmachine/docs/boolean.html">Boolean</a>
                  </li>
                  <li class="toctree-l1"><a class="reference internal" href="/MESHmachine/docs/boolean_cleanup.html">Boolean Cleanup</a>
                  </li>
                  <li class="toctree-l1"><a class="reference internal" href="/MESHmachine/docs/chamfer.html">Chamfer</a>
                  </li>
                  <li class="toctree-l1"><a class="reference internal" href="/MESHmachine/docs/offset.html">Offset</a>
                  </li>
              </ul>
              <p class="caption"><span class="caption-text">Stashes</span></p>
              <ul>
                  <li class="toctree-l1"><a class="reference internal" href="/MESHmachine/docs/create_stash.html">Create Stash</a>
                  </li>
                  <li class="toctree-l1"><a class="reference internal" href="/MESHmachine/docs/view_stashes.html">View Stashes</a>
                  </li>
                  <li class="toctree-l1"><a class="reference internal" href="/MESHmachine/docs/transfer_stashes.html">Transfer Stashes</a>
                  </li>
                  <li class="toctree-l1"><a class="reference internal" href="/MESHmachine/docs/stashes_panel.html">Stashes Panel</a>
                  </li>
                  <li class="toctree-l1"><a class="reference internal" href="/MESHmachine/docs/orphan_stashes.html">Orphan Stashes</a>
                  </li>
                  <li class="toctree-l1"><a class="reference internal" href="/MESHmachine/docs/sweep_stashes.html">Sweep Stashes</a>
                  </li>
                  <li class="toctree-l1"><a class="reference internal" href="/MESHmachine/docs/conform.html">Conform</a>
                  </li>
                  <li class="toctree-l1"><a class="reference internal" href="/MESHmachine/docs/quick_patch.html">Quick Patch</a>
                  </li>
              </ul>
              <p class="caption"><span class="caption-text">Normals</span></p>
              <ul>
                  <li class="toctree-l1"><a class="reference internal" href="/MESHmachine/docs/normal_flatten_straighten.html">Normal Flatten and Straighten</a>
                  </li>
                  <li class="toctree-l1"><a class="reference internal" href="/MESHmachine/docs/normal_transfer.html">Normal Transfer</a>
                  </li>
                  <li class="toctree-l1"><a class="reference internal" href="/MESHmachine/docs/normal_clear.html">Normal Clear</a>
                  </li>
              </ul>
              <p class="caption"><span class="caption-text">Mirror</span></p>
              <ul>
                  <li class="toctree-l1"><a class="reference internal" href="/MESHmachine/docs/symmetrize.html">Symmetrize</a>
                  </li>
                  <li class="toctree-l1"><a class="reference internal" href="/MESHmachine/docs/real_mirror.html">Real Mirror</a>
                  </li>
              </ul>
              <p class="caption"><span class="caption-text">Selection</span></p>
              <ul>
                  <li class="toctree-l1"><a class="reference internal" href="/MESHmachine/docs/lselect.html">LSelect</a>
                  </li>
                  <li class="toctree-l1"><a class="reference internal" href="/MESHmachine/docs/sselect.html">SSelect</a>
                  </li>
                  <li class="toctree-l1"><a class="reference internal" href="/MESHmachine/docs/vselect.html">VSelect</a>
                  </li>
                  <li class="toctree-l1"><a class="reference internal" href="/MESHmachine/docs/select.html">Select</a>
                  </li>
              </ul>
              <p class="caption"><span class="caption-text">Refinement</span></p>
              <ul>
                  <li class="toctree-l1"><a class="reference internal" href="/MESHmachine/docs/wedge.html">Wedge</a>
                  </li>
              </ul>
              <p class="caption"><span class="caption-text">Looptools</span></p>
              <ul>
                  <li class="toctree-l1"><a class="reference internal" href="/MESHmachine/docs/circle_relax.html">Circle and Relax</a>
                  </li>
              </ul>
              <ul>
                <li class="toctree-l1"><a class="reference internal" href="/MESHmachine/docs/changelog.html">Changelog</a>
                </li>
              </ul>
      </div>
    </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap">
      <nav class="wy-nav-top" role="navigation" aria-label="Mobile navigation menu">
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="/MESHmachine/docs/.">MESHmachine</a>
        
      </nav>
      <div class="wy-nav-content">
        <div class="rst-content"><div role="navigation" aria-label="breadcrumbs navigation">
  <ul class="wy-breadcrumbs">
    <li><a href="/MESHmachine/docs/." class="icon icon-home" alt="Docs"></a> &raquo;</li>
    <li class="wy-breadcrumbs-aside">
    </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
            <div class="section" itemprop="articleBody">
              

  <h1 id="404-page-not-found">404</h1>

  <p><strong>Page not found</strong></p>


            </div>
          </div><footer>

  <hr/>

  <div role="contentinfo">
    <!-- Copyright etc -->
  </div>

  Built with <a href="https://www.mkdocs.org/">MkDocs</a> using a <a href="https://github.com/readthedocs/sphinx_rtd_theme">theme</a> provided by <a href="https://readthedocs.org">Read the Docs</a>.
</footer>
          
        </div>
      </div>

    </section>

  </div>

  <div class="rst-versions" role="note" aria-label="Versions">
  <span class="rst-current-version" data-toggle="rst-current-version">
    
    
    
  </span>
</div>
    <script>var base_url = '/MESHmachine/docs/';</script>
    <script src="/MESHmachine/docs/js/theme_extra.js" defer></script>
    <script src="/MESHmachine/docs/js/theme.js" defer></script>
      <script src="/MESHmachine/docs/search/main.js" defer></script>
    <script defer>
        window.onload = function () {
            SphinxRtdTheme.Navigation.enable(true);
        };
    </script>

</body>
</html>
