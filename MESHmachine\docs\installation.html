<!DOCTYPE html>
<html class="writer-html5" lang="en" >
<head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><link rel="canonical" href="https://machin3.io/MESHmachine/docs/installation.html" />
      <link rel="shortcut icon" href="img/favicon.ico" />
    <title>Installation - MESHmachine</title>
    <link rel="stylesheet" href="css/theme.css" />
    <link rel="stylesheet" href="css/theme_extra.css" />
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.5.0/styles/github.min.css" />
        <link href="css/extra.css" rel="stylesheet" />
    
      <script>
        // Current page data
        var mkdocs_page_name = "Installation";
        var mkdocs_page_input_path = "installation.md";
        var mkdocs_page_url = "/MESHmachine/docs/installation.html";
      </script>
    
    <script src="js/jquery-3.6.0.min.js" defer></script>
    <!--[if lt IE 9]>
      <script src="js/html5shiv.min.js"></script>
    <![endif]-->
      <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.5.0/highlight.min.js"></script>
      <script>hljs.initHighlightingOnLoad();</script> 
</head>

<body class="wy-body-for-nav" role="document">

  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side stickynav">
    <div class="wy-side-scroll">
      <div class="wy-side-nav-search">
          <a href="." class="icon icon-home"> MESHmachine
        </a><div role="search">
  <form id ="rtd-search-form" class="wy-form" action="./search.html" method="get">
      <input type="text" name="q" placeholder="Search docs" title="Type search term here" />
  </form>
</div>
      </div>

      <div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="Navigation menu">
              <p class="caption"><span class="caption-text">Get Started</span></p>
              <ul class="current">
                  <li class="toctree-l1"><a class="reference internal" href="index.html">MESHmachine</a>
                  </li>
                  <li class="toctree-l1 current"><a class="reference internal current" href="installation.html">Installation</a>
    <ul class="current">
    <li class="toctree-l2"><a class="reference internal" href="#requirements">Requirements</a>
        <ul>
    <li class="toctree-l3"><a class="reference internal" href="#blender-on-macos">Blender on MacOS</a>
    </li>
    <li class="toctree-l3"><a class="reference internal" href="#blender-on-arch-linux">Blender on Arch Linux</a>
    </li>
    <li class="toctree-l3"><a class="reference internal" href="#latest-meshmachine">Latest MESHmachine</a>
    </li>
        </ul>
    </li>
    <li class="toctree-l2"><a class="reference internal" href="#installation">Installation</a>
        <ul>
    <li class="toctree-l3"><a class="reference internal" href="#1-fresh-installation">1. Fresh Installation</a>
    </li>
    <li class="toctree-l3"><a class="reference internal" href="#2-update-installation">2. Update Installation</a>
    </li>
        </ul>
    </li>
    </ul>
                  </li>
                  <li class="toctree-l1"><a class="reference internal" href="preferences.html">Preferences</a>
                  </li>
                  <li class="toctree-l1"><a class="reference internal" href="whatsnew.html">What's new?</a>
                  </li>
                  <li class="toctree-l1"><a class="reference internal" href="faq.html">FAQ</a>
                  </li>
              </ul>
              <p class="caption"><span class="caption-text">Chamfers and Fillets</span></p>
              <ul>
                  <li class="toctree-l1"><a class="reference internal" href="change_width.html">Change Width</a>
                  </li>
                  <li class="toctree-l1"><a class="reference internal" href="fuse.html">Fuse</a>
                  </li>
                  <li class="toctree-l1"><a class="reference internal" href="flatten.html">Flatten</a>
                  </li>
                  <li class="toctree-l1"><a class="reference internal" href="unfuse.html">Unfuse</a>
                  </li>
                  <li class="toctree-l1"><a class="reference internal" href="refuse.html">Refuse</a>
                  </li>
                  <li class="toctree-l1"><a class="reference internal" href="unchamfer.html">Unchamfer</a>
                  </li>
                  <li class="toctree-l1"><a class="reference internal" href="unbevel.html">Unbevel</a>
                  </li>
                  <li class="toctree-l1"><a class="reference internal" href="unfuck.html">Unf*ck</a>
                  </li>
                  <li class="toctree-l1"><a class="reference internal" href="turn_corner.html">Turn Corner</a>
                  </li>
                  <li class="toctree-l1"><a class="reference internal" href="quad_corner.html">Quad Corner</a>
                  </li>
                  <li class="toctree-l1"><a class="reference internal" href="mark_loop.html">Mark Loop</a>
                  </li>
              </ul>
              <p class="caption"><span class="caption-text">Plugs</span></p>
              <ul>
                  <li class="toctree-l1"><a class="reference internal" href="plugs_introduction.html">Plugs Introduction</a>
                  </li>
                  <li class="toctree-l1"><a class="reference internal" href="plug_creation.html">Plug Creation</a>
                  </li>
                  <li class="toctree-l1"><a class="reference internal" href="advanced_plug_creation.html">Advanced Plug Creation</a>
                  </li>
                  <li class="toctree-l1"><a class="reference internal" href="remove_plugs.html">Remove Plugs</a>
                  </li>
                  <li class="toctree-l1"><a class="reference internal" href="delete_plug.html">Delete Plug</a>
                  </li>
                  <li class="toctree-l1"><a class="reference internal" href="plug_resources.html">Plug Resources</a>
                  </li>
              </ul>
              <p class="caption"><span class="caption-text">Booleans</span></p>
              <ul>
                  <li class="toctree-l1"><a class="reference internal" href="boolean.html">Boolean</a>
                  </li>
                  <li class="toctree-l1"><a class="reference internal" href="boolean_cleanup.html">Boolean Cleanup</a>
                  </li>
                  <li class="toctree-l1"><a class="reference internal" href="chamfer.html">Chamfer</a>
                  </li>
                  <li class="toctree-l1"><a class="reference internal" href="offset.html">Offset</a>
                  </li>
              </ul>
              <p class="caption"><span class="caption-text">Stashes</span></p>
              <ul>
                  <li class="toctree-l1"><a class="reference internal" href="create_stash.html">Create Stash</a>
                  </li>
                  <li class="toctree-l1"><a class="reference internal" href="view_stashes.html">View Stashes</a>
                  </li>
                  <li class="toctree-l1"><a class="reference internal" href="transfer_stashes.html">Transfer Stashes</a>
                  </li>
                  <li class="toctree-l1"><a class="reference internal" href="stashes_panel.html">Stashes Panel</a>
                  </li>
                  <li class="toctree-l1"><a class="reference internal" href="orphan_stashes.html">Orphan Stashes</a>
                  </li>
                  <li class="toctree-l1"><a class="reference internal" href="sweep_stashes.html">Sweep Stashes</a>
                  </li>
                  <li class="toctree-l1"><a class="reference internal" href="conform.html">Conform</a>
                  </li>
                  <li class="toctree-l1"><a class="reference internal" href="quick_patch.html">Quick Patch</a>
                  </li>
              </ul>
              <p class="caption"><span class="caption-text">Normals</span></p>
              <ul>
                  <li class="toctree-l1"><a class="reference internal" href="normal_flatten_straighten.html">Normal Flatten and Straighten</a>
                  </li>
                  <li class="toctree-l1"><a class="reference internal" href="normal_transfer.html">Normal Transfer</a>
                  </li>
                  <li class="toctree-l1"><a class="reference internal" href="normal_clear.html">Normal Clear</a>
                  </li>
              </ul>
              <p class="caption"><span class="caption-text">Mirror</span></p>
              <ul>
                  <li class="toctree-l1"><a class="reference internal" href="symmetrize.html">Symmetrize</a>
                  </li>
                  <li class="toctree-l1"><a class="reference internal" href="real_mirror.html">Real Mirror</a>
                  </li>
              </ul>
              <p class="caption"><span class="caption-text">Selection</span></p>
              <ul>
                  <li class="toctree-l1"><a class="reference internal" href="lselect.html">LSelect</a>
                  </li>
                  <li class="toctree-l1"><a class="reference internal" href="sselect.html">SSelect</a>
                  </li>
                  <li class="toctree-l1"><a class="reference internal" href="vselect.html">VSelect</a>
                  </li>
                  <li class="toctree-l1"><a class="reference internal" href="select.html">Select</a>
                  </li>
              </ul>
              <p class="caption"><span class="caption-text">Refinement</span></p>
              <ul>
                  <li class="toctree-l1"><a class="reference internal" href="wedge.html">Wedge</a>
                  </li>
              </ul>
              <p class="caption"><span class="caption-text">Looptools</span></p>
              <ul>
                  <li class="toctree-l1"><a class="reference internal" href="circle_relax.html">Circle and Relax</a>
                  </li>
              </ul>
              <ul>
                <li class="toctree-l1"><a class="reference internal" href="changelog.html">Changelog</a>
                </li>
              </ul>
      </div>
    </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap">
      <nav class="wy-nav-top" role="navigation" aria-label="Mobile navigation menu">
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href=".">MESHmachine</a>
        
      </nav>
      <div class="wy-nav-content">
        <div class="rst-content"><div role="navigation" aria-label="breadcrumbs navigation">
  <ul class="wy-breadcrumbs">
    <li><a href="." class="icon icon-home" alt="Docs"></a> &raquo;</li>
          <li>Get Started &raquo;</li>
      <li>Installation</li>
    <li class="wy-breadcrumbs-aside">
    </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
            <div class="section" itemprop="articleBody">
              
                <h1 id="_1"></h1>
<h2 id="requirements">Requirements</h2>
<ul>
<li>Windows, MacOS, Linux</li>
<li>Blender 3.6 LTS - 4.3 LTS</li>
</ul>
<div class="admonition danger">
<p class="admonition-title">Experimental Builds</p>
<p><a href="https://builder.blender.org/download/daily/">Experimental</a> Blender builds such as 4.4-alpha are <strong>not supported</strong>, and fixing any issues related to them, will not be a priority, but <a href="faq.html#get-support">reporting them</a> is still encouraged.  </p>
</div>
<h3 id="blender-on-macos">Blender on MacOS</h3>
<p>MacOS users should install Blender properly, by following the <a href="https://docs.blender.org/manual/en/dev/getting_started/installing/macos.html">official instructions</a>.<br />
Avoid running it just from the Downloads folder!<br />
Note that, for <strong>dragging</strong> of files and folders, you need to hold down the <code>COMMAND</code> key.<br />
This will ensure <a href="img/installation/apptranslocation.jpg">AppTranslocation</a><sup id="fnref:1"><a class="footnote-ref" href="#fn:1">1</a></sup> is avoided. </p>
<h3 id="blender-on-arch-linux">Blender on Arch Linux</h3>
<p>Arch Linux users and users of other Arch based or similar rolling release distros are advised to use the <a href="https://blender.org/download">official Blender builds</a>.<br />
The Blender package in the <a href="https://www.archlinux.org/packages/community/x86_64/blender/">Community repository</a> does not supply its own Python, and does not follow <a href="https://docs.blender.org/api/current/info_tips_and_tricks.html#bundled-python-extensions">official recommendations</a>.<br />
As a consequence, the system's python version may not work with MESHmachine.</p>
<h3 id="latest-meshmachine">Latest MESHmachine</h3>
<p>The latest version of MESHmachine is 0.17 - available on <a href="https://gumroad.com/a/164689011/Gwgt">Gumroad</a> and <a href="https://www.blendermarket.com/products/MESHmachine?ref=1051">Blender Market</a>.<br />
See <a href="whatsnew.html">this page</a> the learn what's new in the latest versions, or see the <a href="changelog.html">changelog</a> for the full release history.</p>
<h2 id="installation">Installation</h2>
<h3 id="1-fresh-installation">1. Fresh Installation</h3>
<blockquote>
<p>NO previous version of MESHmachine installed</p>
</blockquote>
<div style="position: relative; width: 100%; height: 0; padding-bottom: 56.25%;">
<iframe src="https://www.youtube.com/embed/Sqzo-kN9aIg" style="position: absolute; top: 0; left: 0; width: 100%; height: 100%;" allowfullscreen seamless frameBorder="0"></iframe>
</div>

<hr />
<h3 id="2-update-installation">2. Update Installation</h3>
<blockquote>
<p>previous version of MESHmachine installed already</p>
</blockquote>
<div class="admonition danger">
<p class="admonition-title">Attention</p>
<p>The video below applies to updating <strong>from</strong> MESHmachine 0.15.0 (or later).<br />
If you are updating from an earlier version of MESHmachine, that doesn't have the <em>Integrated Updater</em> yet, please see <a href="legacy_update_installation.html">these legacy update instructions</a>.</p>
</div>
<div style="position: relative; width: 100%; height: 0; padding-bottom: 56.25%;">
<iframe src="https://www.youtube.com/embed/90djimX0JHA" style="position: absolute; top: 0; left: 0; width: 100%; height: 100%;" allowfullscreen seamless frameBorder="0"></iframe>
</div>

<hr />
<div class="admonition warning">
<p class="admonition-title">Keep in mind</p>
<p>Blender 4.2 supports installation of addons by dropping a .zip file on Blender. This works great generally, but note, that for update installations:</p>
<ol>
<li>You will still need to restart Blender afterwards.</li>
<li>You will <strong>definitely loose all custom created plug assets</strong>, if you have them in the addon's folder, instead of moving them to an external path.</li>
</ol>
</div>
<div class="footnote">
<hr />
<ol>
<li id="fn:1">
<p>Learn more about <a href="https://lapcatsoftware.com/articles/app-translocation.html">AppTranslocation</a>.&#160;<a class="footnote-backref" href="#fnref:1" title="Jump back to footnote 1 in the text">&#8617;</a></p>
</li>
</ol>
</div>
              
            </div>
          </div><footer>
    <div class="rst-footer-buttons" role="navigation" aria-label="Footer Navigation">
        <a href="index.html" class="btn btn-neutral float-left" title="MESHmachine"><span class="icon icon-circle-arrow-left"></span> Previous</a>
        <a href="preferences.html" class="btn btn-neutral float-right" title="Preferences">Next <span class="icon icon-circle-arrow-right"></span></a>
    </div>

  <hr/>

  <div role="contentinfo">
    <!-- Copyright etc -->
  </div>

  Built with <a href="https://www.mkdocs.org/">MkDocs</a> using a <a href="https://github.com/readthedocs/sphinx_rtd_theme">theme</a> provided by <a href="https://readthedocs.org">Read the Docs</a>.
</footer>
          
        </div>
      </div>

    </section>

  </div>

  <div class="rst-versions" role="note" aria-label="Versions">
  <span class="rst-current-version" data-toggle="rst-current-version">
    
    
      <span><a href="index.html" style="color: #fcfcfc">&laquo; Previous</a></span>
    
    
      <span><a href="preferences.html" style="color: #fcfcfc">Next &raquo;</a></span>
    
  </span>
</div>
    <script>var base_url = '.';</script>
    <script src="js/theme_extra.js" defer></script>
    <script src="js/theme.js" defer></script>
      <script src="search/main.js" defer></script>
    <script defer>
        window.onload = function () {
            SphinxRtdTheme.Navigation.enable(true);
        };
    </script>

</body>
</html>
