/*darken level 2 headlines*/
.toctree-l3 a {
	font-weight: bold!important;
	color: #404040!important;
	padding-left:2.2em!important;
}

/*move operator properties closer to their bold "heading"*/
blockquote > p {
	margin: 0 0 1px!important;
}

/* avoid stupid gaps on indented lists */
ul > li > ul {
	margin-top: 0px!important;
	margin-bottom: 0px!important;
}

ul > li > ol {
	margin-top: 0px!important;
	margin-bottom: 0px!important;
}


/* avoid gap in (changelog) lists when keeping empty lines in markdown for better readability*/
li > p {
	margin-bottom: 0px!important;
}

/* minimize gap of dates following version number headline */
div.section > blockquote {
    margin-top: -15px!important;
}
