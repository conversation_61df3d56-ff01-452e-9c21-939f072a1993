import bpy

class GetSupport(bpy.types.Operator):
    bl_idname = "machin3.get_meshmachine_support"
    bl_label = "MACHIN3: Get MESHmachine Support"
    bl_description = "Generate Log Files and Instructions for a Support Request."
    bl_options = {'REGISTER', 'UNDO'}

    def execute(self, context):


        return {'FINISHED'}

    def extend_system_info(self, context, sysinfopath, assetspath):
        pass
        
