{"config": {"indexing": "full", "lang": ["en"], "min_search_length": 3, "prebuild_index": false, "separator": "[\\s\\-]+"}, "docs": [{"location": "index.html", "text": "Introduction Gumroad | Blender Market - Blender Artists | Polycount - Youtube - Twitter - Patreon - eMail cover image based on Humanoid Mecha concept by <PERSON> MESHmachine is a blender mesh modeling addon with a focus on hard surface work without subdivision surfaces. MESHmachine's chamfer and fillet toolset allows for more flexibility, when dealing with fillet-like surfaces, traditionally created with the Bevel and Bridge tools. MESHmachine's approach to fillets is the Fuse tool, which builds transitional surfaces from chamfers, fusing the surfaces on both sides. Doing this, you get the benefits of both - chamfers and fillets - while avoiding their disadvantages. Read on below for an overview of MESHmachine's tools and ideas. Support Attention If you need to get in touch with me to report an error , report tool misbehavior or have another problem READ THIS FIRST . Resources For installation help, see the Installation page. For questions you have or problems you may run into, see the FAQ . To discuss using MESHmachine, please use the blender artists and polycount threads. For plug creation , jump to Plug Creation . To learn about all of MESHmachine's features and tools , you can either check out this playlist on youtube, or better - access those same videos, but in context and with additional information through the sidebar to the left. Features turn chamfers into fillets/bevels and back change the width of a chamfer or bevel create variable fillets and washouts unchamfer and unbevel to go back to a hard edge practically edit existing existing bevels resolve tricky geometry overlaps in cases where two bevels meet flatten multiple polygons based on another polygon or flatten a single polygon based on 3 vertices flatten along a normal or flatten along edges redirect chamfer flow by turning the corners convert triangular bevel corners into quad corners plug details into your mesh build your own plugs and create/buy/sell plug libraries stash objects or face selections, creating backups, that can be brought back or referenced by other tools, without cluttering the scene conveniently add boolean modifiers (incl. split booleans), create automatic stashes when applying the mods, and easily duplicate or instance objects using booleans cleanup applied booleans and create clean perimeter loops around their intersections create flattened, straightend , and transferred custom normals symmetrize meshes including their custom normals turn mirror modifiers into real objects loop select , sharp select and vertex group select , all using a single keymap easily add wedges best documentation in the business Overview Chamfers Chamfers (flat bevels) are easy to create, easy to manipulate and are easy to (loop) select. They are very useful to quickly block out more complex forms and already contain all the important information in regards to the flow of surfaces and edges as well as the chamfer width. Chamfers are also a dead end, if you want to further refine them later on, unless you get down to the edge or vertex level, which is time consuming. Traditionally, they can't be easily turned into fillets(rounded bevels) and removing the chamfer polygons to bridge the open edge loops, bedides being tedious, often produces results without good surface continuity or with unnessesary edges. The overshooting is relatively easy to fix, IF you actually want to create a chamfer, but gets harder (support edges), if you want a rounded bevel. So, what if you could turn a chamfer into a bevel? Fillets Fillets (round bevels) are also easy to create, using Blender's Bevel tool. But once created, they are hard or downright impossible to manipulate. Depending on the density, they are also significantly harder to select. Fillets are excellent for refined, finished forms however, but due to how hard they are to manipulate, using them early on usually means you are locking the design down. Changing it later becomes so hard, you're likely to deny yourself that option completely. As a result your design may suffer. Fuse Using MESHmachine's Fuse and Unfuse tools, you can move between chamfers and fillets back and forth effortlessly - you can turn a chamfer into a fillet and a fillet back into a chamfer. This allows for significant gains in flexibility, because - remember - a chamfer is trivial to manipulate and once you've done so, you can turn it into a Fillet again. Moreover, the width of a chamfer will directly determine the width of a fillet(the radius if you want, but it's not precisly circular and depends on the tension setting). This in turn allows for the creation of variable fillets and even washouts, which are impossible to do with the bevel tool and modifier. tasks like these are usually pretty tedious, but are rather trivial with MESHmachine Plugs Plugs are mesh inserts, that in combination with normal transfers, can produce flawless detailing, even on curved surfaces and even on medium or low density models. MESHmachine provides tools to manage plug libraries and tools to create your own plugs well. Stashes MESHmachine introduced the concept of Stashes , which are basically object backups, that can also be referenced by other tools, like Conform . What's special about them, is the out-of-your-way approach, which means they won't clutter the scene and instead are accessed, edited, swapped or retrieved using a modal stash viewer or the panel in the sidebar. Booleans MESHmachine provides tools to conveniently set up boolean operations in object mode using Blender's boolean modifier. It adds a 4th boolean split mode and allows for easy duplication or instancing of objects carrying boolean modifiers including their cutter objects. In addition it facilitates applying these modifiers and creates stashes from the boolean operants automatically. Beyond that, MESHmachine supplies a few tools, that are helpful after boolean operations. Boolean Cleanup merges vertices on the transitional edge between two boolean operators. The Chamfer and Offset tools help to create clean boundary topology, which in turn is beneficial for tools like Bevel, Change Width and Fuse / Refuse . Normal Tools In addition to modeling tools, MESHmachine has so far added 4 tools to manipulate vertex normals. Traditionally used in game art, if at all, this makes custom normals accessible in medium and high resolution mesh modeling as well and further loosens topology constraints in non-subd modeling. The Normal Transfer tool in combination with stashes, mirrors behavior of parametric solids and nurbs applicatinos and can produce the similar quality in terms of shading. Mirroring Utilizing custom normals, requires additional tooling on the mirroring front, as Blender's symmetrize tool does not support custom normals. MESHmachine's Symmetrize adds those missing normal mirror capabilities, and allows for efficient single-keymap \"flick symmetrizing\" all all 6 object space directions. As for the mirror modifier, MESHmachine's Real Mirror tool can create real geometry with properly mirrored object origins and custom normals from objects carrying mirror mods. This then enables correct object-space texturing for mirrored geometry. Selection Loop selecting in context of non-subd or ngon modelling can be a challenge, because Blender's native loop select tool is aimed at quad topology. That's why MESHmachine introduces an angle-based loop select tool called LSelect , that is free of quad topology constraints. In addition, SSelect allows for easy selections of connected sharp edges. VSelect provides a quick, visual way to select geometry stored in vertex groups. This is especially useful in combination with Plugs and Normal Transfers, but can also be handy for rigging/skinning. Tying these three as well as Blender's native loop select tool together, is the Select tool, which is a wrapper around all 4, thereby enabling conditional use of each one via a single keymap. Looptools Looptools is a favourite addon of many blender users and ships with blender's default set of addons. MESHmachine provides modal wrappers , so Looptools' Circle and Relax can be used in the same fashion as MESHmachine's tools.", "title": "MESHmachine"}, {"location": "index.html#_1", "text": "", "title": ""}, {"location": "index.html#introduction", "text": "Gumroad | Blender Market - Blender Artists | Polycount - Youtube - Twitter - Patreon - eMail cover image based on Humanoid Mecha concept by <PERSON> MESHmachine is a blender mesh modeling addon with a focus on hard surface work without subdivision surfaces. MESHmachine's chamfer and fillet toolset allows for more flexibility, when dealing with fillet-like surfaces, traditionally created with the Bevel and Bridge tools. MESHmachine's approach to fillets is the Fuse tool, which builds transitional surfaces from chamfers, fusing the surfaces on both sides. Doing this, you get the benefits of both - chamfers and fillets - while avoiding their disadvantages. Read on below for an overview of MESHmachine's tools and ideas.", "title": "Introduction"}, {"location": "index.html#support", "text": "Attention If you need to get in touch with me to report an error , report tool misbehavior or have another problem READ THIS FIRST .", "title": "Support"}, {"location": "index.html#resources", "text": "For installation help, see the Installation page. For questions you have or problems you may run into, see the FAQ . To discuss using MESHmachine, please use the blender artists and polycount threads. For plug creation , jump to Plug Creation . To learn about all of MESHmachine's features and tools , you can either check out this playlist on youtube, or better - access those same videos, but in context and with additional information through the sidebar to the left.", "title": "Resources"}, {"location": "index.html#features", "text": "turn chamfers into fillets/bevels and back change the width of a chamfer or bevel create variable fillets and washouts unchamfer and unbevel to go back to a hard edge practically edit existing existing bevels resolve tricky geometry overlaps in cases where two bevels meet flatten multiple polygons based on another polygon or flatten a single polygon based on 3 vertices flatten along a normal or flatten along edges redirect chamfer flow by turning the corners convert triangular bevel corners into quad corners plug details into your mesh build your own plugs and create/buy/sell plug libraries stash objects or face selections, creating backups, that can be brought back or referenced by other tools, without cluttering the scene conveniently add boolean modifiers (incl. split booleans), create automatic stashes when applying the mods, and easily duplicate or instance objects using booleans cleanup applied booleans and create clean perimeter loops around their intersections create flattened, straightend , and transferred custom normals symmetrize meshes including their custom normals turn mirror modifiers into real objects loop select , sharp select and vertex group select , all using a single keymap easily add wedges best documentation in the business", "title": "Features"}, {"location": "index.html#overview", "text": "", "title": "Overview"}, {"location": "index.html#chamfers", "text": "Chamfers (flat bevels) are easy to create, easy to manipulate and are easy to (loop) select. They are very useful to quickly block out more complex forms and already contain all the important information in regards to the flow of surfaces and edges as well as the chamfer width. Chamfers are also a dead end, if you want to further refine them later on, unless you get down to the edge or vertex level, which is time consuming. Traditionally, they can't be easily turned into fillets(rounded bevels) and removing the chamfer polygons to bridge the open edge loops, bedides being tedious, often produces results without good surface continuity or with unnessesary edges. The overshooting is relatively easy to fix, IF you actually want to create a chamfer, but gets harder (support edges), if you want a rounded bevel. So, what if you could turn a chamfer into a bevel?", "title": "Chamfers"}, {"location": "index.html#fillets", "text": "Fillets (round bevels) are also easy to create, using <PERSON><PERSON><PERSON>'s Bevel tool. But once created, they are hard or downright impossible to manipulate. Depending on the density, they are also significantly harder to select. Fillets are excellent for refined, finished forms however, but due to how hard they are to manipulate, using them early on usually means you are locking the design down. Changing it later becomes so hard, you're likely to deny yourself that option completely. As a result your design may suffer.", "title": "<PERSON><PERSON><PERSON>"}, {"location": "index.html#fuse", "text": "Using MESHmachine's Fuse and Unfuse tools, you can move between chamfers and fillets back and forth effortlessly - you can turn a chamfer into a fillet and a fillet back into a chamfer. This allows for significant gains in flexibility, because - remember - a chamfer is trivial to manipulate and once you've done so, you can turn it into a Fillet again. Moreover, the width of a chamfer will directly determine the width of a fillet(the radius if you want, but it's not precisly circular and depends on the tension setting). This in turn allows for the creation of variable fillets and even washouts, which are impossible to do with the bevel tool and modifier. tasks like these are usually pretty tedious, but are rather trivial with MESHmachine", "title": "<PERSON><PERSON>"}, {"location": "index.html#plugs", "text": "Plugs are mesh inserts, that in combination with normal transfers, can produce flawless detailing, even on curved surfaces and even on medium or low density models. MESHmachine provides tools to manage plug libraries and tools to create your own plugs well.", "title": "Plugs"}, {"location": "index.html#stashes", "text": "MESHmachine introduced the concept of Stashes , which are basically object backups, that can also be referenced by other tools, like Conform . What's special about them, is the out-of-your-way approach, which means they won't clutter the scene and instead are accessed, edited, swapped or retrieved using a modal stash viewer or the panel in the sidebar.", "title": "Stashes"}, {"location": "index.html#booleans", "text": "MESHmachine provides tools to conveniently set up boolean operations in object mode using <PERSON><PERSON><PERSON>'s boolean modifier. It adds a 4th boolean split mode and allows for easy duplication or instancing of objects carrying boolean modifiers including their cutter objects. In addition it facilitates applying these modifiers and creates stashes from the boolean operants automatically. Beyond that, MESHmachine supplies a few tools, that are helpful after boolean operations. Boolean Cleanup merges vertices on the transitional edge between two boolean operators. The Chamfer and Offset tools help to create clean boundary topology, which in turn is beneficial for tools like Bevel, Change Width and Fuse / Refuse .", "title": "Booleans"}, {"location": "index.html#normal-tools", "text": "In addition to modeling tools, MESHmachine has so far added 4 tools to manipulate vertex normals. Traditionally used in game art, if at all, this makes custom normals accessible in medium and high resolution mesh modeling as well and further loosens topology constraints in non-subd modeling. The Normal Transfer tool in combination with stashes, mirrors behavior of parametric solids and nurbs applicatinos and can produce the similar quality in terms of shading.", "title": "Normal Tools"}, {"location": "index.html#mirroring", "text": "Utilizing custom normals, requires additional tooling on the mirroring front, as Blender's symmetrize tool does not support custom normals. MESHmachine's Symmetrize adds those missing normal mirror capabilities, and allows for efficient single-keymap \"flick symmetrizing\" all all 6 object space directions. As for the mirror modifier, MESHmachine's Real Mirror tool can create real geometry with properly mirrored object origins and custom normals from objects carrying mirror mods. This then enables correct object-space texturing for mirrored geometry.", "title": "Mirroring"}, {"location": "index.html#selection", "text": "Loop selecting in context of non-subd or ngon modelling can be a challenge, because Blender's native loop select tool is aimed at quad topology. That's why MESHmachine introduces an angle-based loop select tool called LSelect , that is free of quad topology constraints. In addition, SSelect allows for easy selections of connected sharp edges. VSelect provides a quick, visual way to select geometry stored in vertex groups. This is especially useful in combination with Plugs and Normal Transfers, but can also be handy for rigging/skinning. Tying these three as well as Blender's native loop select tool together, is the Select tool, which is a wrapper around all 4, thereby enabling conditional use of each one via a single keymap.", "title": "Selection"}, {"location": "index.html#looptools", "text": "Looptools is a favourite addon of many blender users and ships with blender's default set of addons. MESHmachine provides modal wrappers , so Looptools' Circle and Relax can be used in the same fashion as MESHmachine's tools.", "title": "Looptools"}, {"location": "_blank.html", "text": "Blank shortcut y mode The Blank tool is used to . Selection Some geometry. Using Blank", "title": " blank"}, {"location": "_blank.html#blank", "text": "shortcut y mode The Blank tool is used to .", "title": "Blank"}, {"location": "_blank.html#selection", "text": "Some geometry.", "title": "Selection"}, {"location": "_blank.html#using-blank", "text": "", "title": "Using Blank"}, {"location": "advanced_plug_creation.html", "text": "Advanced Plug Creation Plugs can consist of more components than just the plug mesh, the plug handle and plug subsets. Deformers For instance, you can add a deformer, which is a support object that unlocks an alternative deformation approach, usually producing better results. Occluders Occluders are another type of support objects, unlike Deformers, they don't contribute anything to the plugging itself. They are used to properly render a plug thumbnail, in case the plug has a significant under structure. Hook Plugs Hook Plugs are dynamic plugs and allow for geometry manipulation via hook modifiers, before plugging. As a hook plug is expanded, no new geometry is added. Keep that in mind, because it can affect how well the plug can deform. Array Plugs Array Plugs are dynamic plugs, that do add new geometry, as the plug is expaned. This is done via Blender's array modifier. Array plugs are the most complex type of plug so far. Set and Clear Plug Properties Plug components carry certain propeties, which are used internally by MESHmachine. This is how MESHmachine knows what the plug mesh is, what object the handle or deformer is, etc. Advanced Plug Creation especially requires you to set some of these properties manually. This is done via the Set Plug Props tool. Select an object, run the tool and change the properties in the redo panel. The Clear Plug Props tool is used to completely reset an object and remove all its plug properties. This is useful, if you want to start afresh, without loosing what you have modeled already. And It's necessary for array cap objects. Since the caps are created from the same objects as the plug mesh, handle or deformer, they carry the same properties. If you don't clear the cap props, MESHmachine doesn't know which is the real mesh, handle or deformer.", "title": "Advanced Plug Creation"}, {"location": "advanced_plug_creation.html#advanced-plug-creation", "text": "Plugs can consist of more components than just the plug mesh, the plug handle and plug subsets.", "title": "Advanced Plug Creation"}, {"location": "advanced_plug_creation.html#deformers", "text": "For instance, you can add a deformer, which is a support object that unlocks an alternative deformation approach, usually producing better results.", "title": "Deformers"}, {"location": "advanced_plug_creation.html#occluders", "text": "Occluders are another type of support objects, unlike Deformers, they don't contribute anything to the plugging itself. They are used to properly render a plug thumbnail, in case the plug has a significant under structure.", "title": "Occluders"}, {"location": "advanced_plug_creation.html#hook-plugs", "text": "Hook Plugs are dynamic plugs and allow for geometry manipulation via hook modifiers, before plugging. As a hook plug is expanded, no new geometry is added. Keep that in mind, because it can affect how well the plug can deform.", "title": "Hook Plugs"}, {"location": "advanced_plug_creation.html#array-plugs", "text": "Array Plugs are dynamic plugs, that do add new geometry, as the plug is expaned. This is done via Blender's array modifier. Array plugs are the most complex type of plug so far.", "title": "A<PERSON>y Plugs"}, {"location": "advanced_plug_creation.html#set-and-clear-plug-properties", "text": "Plug components carry certain propeties, which are used internally by MESHmachine. This is how MESHmachine knows what the plug mesh is, what object the handle or deformer is, etc. Advanced Plug Creation especially requires you to set some of these properties manually. This is done via the Set Plug Props tool. Select an object, run the tool and change the properties in the redo panel. The Clear Plug Props tool is used to completely reset an object and remove all its plug properties. This is useful, if you want to start afresh, without loosing what you have modeled already. And It's necessary for array cap objects. Since the caps are created from the same objects as the plug mesh, handle or deformer, they carry the same properties. If you don't clear the cap props, MESHmachine doesn't know which is the real mesh, handle or deformer.", "title": "Set and Clear Plug Properties"}, {"location": "boolean.html", "text": "Add & Apply Boolean shortcut ya object mode MESHmachine provides a quick way to add boolean modifiers, as well as apply them. If you apply boolean modifers this way, MESHmachine will automatically create stashes from operant objects. Split Mode Version 0.8 of MESHmachine introduced the Split Boolean mode, which in addition to setting up the boolean modifiers, also creates a duplicate of the active object, to create the second, split-away part. Splitting will leave the object meshes, as well as the cutter meshes as instances. This means it's easy to adjust the active object's mesh or the cutter after a split was performed, and the changes will automatically carry through to the other part. To get rid of the instances, you can use the new Make Unique tool. Boolean Apply will also correctly deal with instanced splt parts. Selection At least 2 objects, whith one being active, when adding a boolean modifier One or multiple objects with existing boolean modifiers when applying the modifiers Using Add & Apply Boolean Duplicate shortcut yd object mode Duplicate Boolean is used to duplicate or instance objects using booleans. As of version 0.10 this is fully recursive, meaning it will work correctly even if the operand objects aka cutters themselves use boolen modifiers. The tool is especially useful if cutters - are hidden, as you still only need to have the main object selected. Modes Default: Duplicate the selected objects and all its cutters ALT : Create instances instead of duplicates Selection At least 1 object with a boolean modifier Make Unique shortcut ym object mode Make Unique is a tool specifically created to deal with Split Booleans, or rather the instanced meshes they create. It can also be used for instanced booleans created by the Duplicate Boolean tool. Selection Any number of objects using an instanced mesh", "title": "Boolean"}, {"location": "boolean.html#_1", "text": "", "title": ""}, {"location": "boolean.html#add-apply-boolean", "text": "shortcut ya object mode MESHmachine provides a quick way to add boolean modifiers, as well as apply them. If you apply boolean modifers this way, MESHmachine will automatically create stashes from operant objects.", "title": "Add &amp; Apply Boolean"}, {"location": "boolean.html#split-mode", "text": "Version 0.8 of MESHmachine introduced the Split Boolean mode, which in addition to setting up the boolean modifiers, also creates a duplicate of the active object, to create the second, split-away part. Splitting will leave the object meshes, as well as the cutter meshes as instances. This means it's easy to adjust the active object's mesh or the cutter after a split was performed, and the changes will automatically carry through to the other part. To get rid of the instances, you can use the new Make Unique tool. Boolean Apply will also correctly deal with instanced splt parts.", "title": "Split Mode"}, {"location": "boolean.html#selection", "text": "At least 2 objects, whith one being active, when adding a boolean modifier One or multiple objects with existing boolean modifiers when applying the modifiers", "title": "Selection"}, {"location": "boolean.html#using-add-apply-boolean", "text": "", "title": "Using Add &amp; Apply Boolean"}, {"location": "boolean.html#duplicate", "text": "shortcut yd object mode Duplicate Boolean is used to duplicate or instance objects using booleans. As of version 0.10 this is fully recursive, meaning it will work correctly even if the operand objects aka cutters themselves use boolen modifiers. The tool is especially useful if cutters - are hidden, as you still only need to have the main object selected.", "title": "Duplicate"}, {"location": "boolean.html#modes", "text": "Default: Duplicate the selected objects and all its cutters ALT : Create instances instead of duplicates", "title": "Modes"}, {"location": "boolean.html#selection_1", "text": "At least 1 object with a boolean modifier", "title": "Selection"}, {"location": "boolean.html#make-unique", "text": "shortcut ym object mode Make Unique is a tool specifically created to deal with Split Booleans, or rather the instanced meshes they create. It can also be used for instanced booleans created by the Duplicate Boolean tool.", "title": "Make Unique"}, {"location": "boolean.html#selection_2", "text": "Any number of objects using an instanced mesh", "title": "Selection"}, {"location": "boolean_cleanup.html", "text": "Boolean Cleanup shortcut ya edit mode The Boolean Cleanup tool is used to merge vertices at the intersection of boolean operations. It does that by sorting vertices according to the edgeson each side. Often with boolean operations, you want to maintain the vertices from one of the operators, and merge the other verts to those. Selection Select a loop of edges or vertices. In the context of booleans, this will be a closed loop, a cyclic selection, but the tool will also work on non-cyclic selections. edge or vertex loop, cyclic or non-cyclic Using Boolean Cleanup", "title": "Boolean Cleanup"}, {"location": "boolean_cleanup.html#boolean-cleanup", "text": "shortcut ya edit mode The Boolean Cleanup tool is used to merge vertices at the intersection of boolean operations. It does that by sorting vertices according to the edgeson each side. Often with boolean operations, you want to maintain the vertices from one of the operators, and merge the other verts to those.", "title": "Boolean Cleanup"}, {"location": "boolean_cleanup.html#selection", "text": "Select a loop of edges or vertices. In the context of booleans, this will be a closed loop, a cyclic selection, but the tool will also work on non-cyclic selections. edge or vertex loop, cyclic or non-cyclic", "title": "Selection"}, {"location": "boolean_cleanup.html#using-boolean-cleanup", "text": "", "title": "Using Boolean Cleanup"}, {"location": "chamfer.html", "text": "<PERSON><PERSON><PERSON> The Chamfer tool is used to create a chamfer or flat bevel at the intersection of boolean operators. It is not a general purpose chamfer tool, like blenders bevel tool is. <PERSON><PERSON>fer can deal with tricky geometry overlaps and significantly reduce manual cleanup work as a result of these overlaps. It also has independent, per-side loop slide properties. Note It is wise to run BooleanCleanup before using the <PERSON><PERSON><PERSON> tool. Selection Select a loop of edges or vertices. It should be a cyclic selection, otherwise non-manifold geometry will be created. cyclic edge loop at boolean intersection, a vert loop is fine as well Using Cha<PERSON>fer", "title": "<PERSON><PERSON><PERSON>"}, {"location": "chamfer.html#chamfer", "text": "The Chamfer tool is used to create a chamfer or flat bevel at the intersection of boolean operators. It is not a general purpose chamfer tool, like blenders bevel tool is. Chamfer can deal with tricky geometry overlaps and significantly reduce manual cleanup work as a result of these overlaps. It also has independent, per-side loop slide properties. Note It is wise to run BooleanCleanup before using the Chamfer tool.", "title": "<PERSON><PERSON><PERSON>"}, {"location": "chamfer.html#selection", "text": "Select a loop of edges or vertices. It should be a cyclic selection, otherwise non-manifold geometry will be created. cyclic edge loop at boolean intersection, a vert loop is fine as well", "title": "Selection"}, {"location": "chamfer.html#using-chamfer", "text": "", "title": "Using <PERSON>"}, {"location": "change_width.html", "text": "Change Width shortcut yw edit mode Using the Change Width tool you can adjust the width of an existing chamfer. Usually, that can only be done by manually sliding the edges of a chamfer. The Change Width tool simplifies that process. Selection Poly strip - a row of polygons - along the chamfer. The best way to select a chamfer is usually by loop selecting it - by default via Alt + Select Mouse , while pointing at an sweep edge of the chamfer. poly strip selection along chamfer Single polygons can also be selected, in which case the direction will be determined by edge length. As with the Fuse tool, you can can also reverse the direction in the redo/tool properties panel. Using Change Width", "title": "Change Width"}, {"location": "change_width.html#change-width", "text": "shortcut yw edit mode Using the Change Width tool you can adjust the width of an existing chamfer. Usually, that can only be done by manually sliding the edges of a chamfer. The Change Width tool simplifies that process.", "title": "Change Width"}, {"location": "change_width.html#selection", "text": "Poly strip - a row of polygons - along the chamfer. The best way to select a chamfer is usually by loop selecting it - by default via Alt + Select Mouse , while pointing at an sweep edge of the chamfer. poly strip selection along chamfer Single polygons can also be selected, in which case the direction will be determined by edge length. As with the Fuse tool, you can can also reverse the direction in the redo/tool properties panel.", "title": "Selection"}, {"location": "change_width.html#using-change-width", "text": "", "title": "Using Change Width"}, {"location": "changelog.html", "text": "0.17.0 2024-11-22 LSelect tool support loop selection step limit Unfuck support multiple independent selections prevent propagation when working with edge-only geometry and for multiple selections Fuse, Refuse, QuadCorner tools keep material indices (if multiple are used on a mesh) LoopTools wrappers add Space wrapper Circle wrapper fix hang when using multiple independent selections remove fix midpoint option for now Boolean tool fix TWO key not working for scrolling down add update available indication in 3D view's sidebar and in addon prefs 0.16.0 2024-07-10 CreateStash tool apply mods on stashes, depending on mode your are in stashing from object mode: apply modifiers on stash(es) keep them by holding ALT key stashing from edit mode: keep modifiers on stash apply them by holding ALT key unless you stash a specific face selection only the tools tooltip reflects these changes, so reference that if you are ever unsure re-enable fading wire drawing, accidentally left disabled in 0.15.4 BooleanApply tool properly support redoing now support individually toggling whether mods should be applied on original and operand object stashes by default mods are not applied on the original object's stash and are applied on the operand objects' stashes Symmetrize tool when not mirroring vertex groups, remove the association on the affected verts, instead of just setting the weight to 0 AddBoolean tool fix import when setting up AutoSmooth in 4.1+ Plug tool fix exception when applying normal transfer as part of the plugging addon preferences in Blender 4.2, when activating LoopTools wrappers in addon prefs, support automatically installing it from Blender extensions repo on first addon registration, try to setup the MACHIN3tools matcap_shiny_red.exr for the NormalTransfer tool, if it can be found 0.15.4 2024-05-16 bad day 0.15.3 2024-05-16 Fuse tool fix exception in BRIDGE mode, introduced accidentally in 0.15.2 Symmetrize tool when not mirroring custom normals, expose option to mirror vgroups, but default to False fix exception when mirroring custom normals, when using transfer method to fix the center seam improve HyperCursor integration Boolean tool simplify and speed up AutoSmooth setup, when toggling smoothing using S key Wedge tool improve HyperCursor integration CreateStash tool fix issues when trying to stash non-mesh objects preferences add notes for ALT + LEFTMOUSE keymap, used by default for the Select wrapper, and should be remapped for people using ALT for navigation 0.15.2 2024-04-10 Select tool/wrapper fix issue in always_loop_select mode where you can't select additional edges Boolean tool in Blender 4.1 when adding Auto Smooth mod, while existing Auto Smooth node trees are in the file, use the API which is faster! , rather than the Blender op to add the mod NormalTransfer tool prevent exception when having a selection of only sharp edges (no faces) this is rather pointless, as you really want to select faces next to sharp edges, but now it longer errors out OffsetCut tool fix rare exception when custom data layers are still present from a previous run remove Blender-auto-added Auto Smooth mods, when bringing Plugs into the scene Fuse, Refuse, Unfuse, Unchamfer, Unbevel tools support creating HyperCursor unreleased geometry gizmos Fuse, Refuse tools support maintaining vertex groups on sweep verts addon preferences update integrated updater to expose the Re-Scan button even if no update is found yet 0.15.1 2024-03-19 Boolean tool in Blender 4.1 when checking for existing Auto Smooth mods in the stack, that deviate from standard naming, support finding ones with index suffix in the node tree name 0.15 2024-03-18 support Blender 4.1 auto smooth and custom normal related fixes in Symmetrize tool Nornmal Flatten and Straighten tools Normal Transfer tool Normal Clear tool RealMirror tool Plug tool Boolean tool Boolean tool support modifier based Auto Smooth in 4.1 when toggling (auto) smooth using S key, insert the mod towards the end of the stack, but always before Mirror and Array mods init operator's auto smooth state and angle, from active object when finishing with LMB unselect the active hide cutters from rendering via cycles visibility settings too now when canceling restore everything accordingly to its initial state flesh out statusbar Symmetrize tool expose redundant center edge removal threshold angle increase it slightly from 0 to 0.05 to be more tolerant Select tool/wrapper add Always Loop Select toggle by default connected Sharps are selected, if entire edge selection consists of sharp edges, otherwise an angle based loop selection is done with Always Loop Select enabled now, this behavior is overidden, so you don't constantly have to toggle back to loop selecting, when working on sharp edges Stashes improve how stashes HUD is drawn at the top of the 3D view properly offset HUD downs depending on region_overlap pref, theme header alpha, and header and tool header positioning furthermore offset stashes HUD down, if MACHIN3tools focus tool active, and has its own HUD drawn ViewStashes tool improve edit stash object HUD in the same way handle area and space data reference loss due to area maximizing or workspace space changes more gracefully still not recommended to do, while in edit stash mode though CreatePlug, AddPlugToLibrary tools ensure plug objects are properly render-able for the thumbnail Unchamfer tool fix typo, causing exception in debug mode handlers rework when and how handler logic is executed add asset drop cleanup handler (previously in MACHIN3tools) to automatically unlink stash objects after dropping and asset using stashes from the asset browser GetSupport tool improve the readme.html file, generated by the tool, providing more details on how to get to the system console in Bender 4.1 (and 3.5) open the readme.html in the Browser automatically addon preferences place Get Support button at top of addon prefs add custom updater NOTE: since it's only introduced now in 0.15, it will only be of use for upcoming releases, so can't be used to install this very 0.15 update yet allows for very easy addon update installation from .zip file, and from inside of Blender, instead of manually from the file browser finds matching .zip file(s) in home and Downloads folder allows selecting one of them them or manually selecting a file in any other location extracts the file to a temporary location, and installs update when quitting Blender like a manual update installation from the filebrowser, this maintains previous addon settings and custom keys see installation instructions for details supports - and defaults to - keeping the current assets to prevent accidental loss fix issue with thankyou note after manual installation without removing previous installation fix plug library unloading messages sell being shown despite registration_debug pref being disabled v0.14 2023-11-14 OffsetCut tool experimental remove dependency on face maps, as they were removed in 4.0 Wedge tool support HyperCursor 0.9.15 pre-release :) alongside previous versions addon preferences add option to control whether addon (un)registration confirmation is written to the terminal simplify and refactor how the various settings are drawn add HyperCursor to about section in addon preferences addon preferences - add new nag screen draw thank you message direction above the prefs, instead of in a popup for 5 minutes, then disappear forever fully support Blender 4.0 drop dpi arg in blf.size() support new shader names support new bevel weight custom data layer ensure modal HUDs are only drawn on active 3D view, not others fix np.float issue with newer numpy versions silence reload_modules() debug output for good v0.13 2023-06-28 bump minimum Blender version to 3.6 Normal Transfer tool add previously removed NEAREST NORMAL and NEAREST POLY NORMAL mapping methods again they can be useful when transfering normals next to sharp edges Symmetrize tool fix issue when encountering non-manifold center edges at the center lower precision a little when determining which center edges are redundant add pre-0.6 Tutorial.blend file to 3D view's sidebar MESHmachine help panel this file contains simple test cases for the tools of the fillet tool set switch all timer modals to new system clock based method fixes a long time issue, where timer modals would sometimes run much faster than they should, which was especially annoying for the Add Boolean tool update HUD and VIEW3D drawing take system's UI scaling into account for all HUDs preferences add show_sidebar_panel toggle update about page remove bgl completely v0.12 2022-12-30 bump minimum Blender version to 3.3 BooleanDuplicate redo completely and now duplicate the entire \"object tree\", which includes all mod objects, even those that aren't parented no longer initiate the translate tool at the end, to keep the operator props in the redo panel accessible support local view Boolean SPLIT ensure all the parenting, all (parented) mod objects, all drivers, hooks etc are proberly and separately reproduced on the split object add displace mod using a strength of 0 on the split off part change default auto smooth angle to 20 degrees instead of 30 BooleanApply remove cutter children as well Symmetrize add ability to remove redundant center edges, enabled by default only available if remove and partial are disabled, and if there are no custom normals Fuse, Change Width, etc. take object scaling into account to determine mouse sensitivity various HyperCursor integrations v0.11.2 2022-04-30 resolve odd 3.1 related crashes, hopefully add thank you note to addon preferences v0.11.1 2022-04-24 Boolean disable renderability for cutters a few small tweaks and fixes v0.11 2022-03-09 support Blender 3.1 Plug, NormalTransfer and Conform tools use new generalized vgroup management approach, required due to changes in 3.0+ fix automatic legacy stash update v0.10 2021-12-24 Symmetrize tool add mode to remove half the mesh instead of symmetrizing, toggle via X key add mode to symmetrize/remove only the selected parts of a mesh, toggle via S key take HUD scale prefs into account for drawing of flick distance and HUD labels BooleanDuplicate tool support recursive duplication and instancing, allowing for more more complex boolean setups, where operands themselves have boolean mods fix exception when encountering objects without a data block, such as group empties MakeUnique tool fix poll for objects without a data block, such as group empties MESHmachine menu fix is_instance poll for for non-mesh objects, such as group empties v0.9.1 2021-12-18 Flatten tool support multi face flattening with Pick Shortest Path, Box or Circle Selections prevent invalid selections Unchamfer tool default to setting sharp edges only if selected faces are smooth NormalTransfer and Conform tools use temporary workaround related to this Blender 3.0 bug fix Split and Delete tools not appearing in MESHmachine's menu in Blender 3.0 v0.9 2021-11-25 support Blender 3.0 add BooleanDuplicate tool create instances via ALT BooleanApply tool support multi object selections Unfuse and Unbevel tools default to setting sharp edges only if selected faces are smooth improve GetSupport tool addon preferences add option for legacy line smoothing remove legacy code for Blender pre-2.93 drop bgl module use for view3d drawing (except for legacy line smoothing) v0.8.2 2021-09-10 Wedge disable debug output fix issues on some linux systems v0.8.1 2021-08-23 fix Wedge on rotated objects v0.8 2021-08-22 add Wedge tool Boolean tool add SPLIT mode add MakeUnique tool useful for instanced meshes as created by Split Booleans BooleanApply tool support applying mods with instanced meshes, such as ones created by Split Booleans support applying mods whose objects are used by multiple booleans name stashes based on boolean operation this allows you to later easily filter out (and remove) stashes of the (at the time) active objects only apply mods, that are visible in the viewport ViewStashes and ViewOrphanStashes tools support setting cursor to stash Plug tool support re-targeting subset shrinkwrap mods, if their target is the plug object AddPlugToLibrary tool work around issues with linked thumbnail scene contents introduced in 2.90 Lselect tool inverse min-angle for better UX, increasing the angle now grows the selection Symmetrize tool fix issue with the flick HUD, if object origin was far behind view replace previous parenting and unparenting logic use safe matrix inversion everywhere v0.7.2 2021-03-25 support stashing evaluated meshes using ALT key when swapping stash, parent active's children - including decals - to swapped stash object fix subset plug creation issue fix typos v0.7.1 2021-03-01 add NDOF(3d mouse) support for viewport navigation in all modal tools add optional stashes HUD offset in preferences NormalTransfer and Conform tools reverse stash scroll direction, just like in the ViewStashes tool fix Quickpatch raycast error, when filtering out decals but not having DM installed fix 2.93 issues due to change in bpy.props representation before registration v0.7 2021-02-20 add Boolean tool add BooleanApply tool add Select tool a selection wrapper that picks VSelect, SSelect, LSelect or Blender's native loop select depending on existing selection context keymapped to ALT + LMB by default add QuickPatch tool add DeletePlug tool with only the handles selected, conveniently delete one or multiple plugs (incl. related and potentiall hidden deformers and occluders) use the X \"shortcut\" in the MESHmachine object menu add SweepStashes tool (sidepanel) clean up a scene after appending an object with stashes from another blend file Symmetrize tool - add flick mode symmetrize in any of the 6 local space directions using a single keymap LSelect tool support selecting multiple loops at once or one after another prevent issues with zero length edges expose Stashes in 3D view's sidepanel show, (re)name, swap and remove an object's stashes CreateStash tool support stashing face selections support finishing early and canceling ViewStashes tool add stash swapping capability support MACHIN3tools grouping, when swapping add stash clearing capability support setting wireframe alpha support canceling when stashes were retrieved select only retrieved stash objects when finishing ViewOrphanStashes tool add stash clearing capability TransferStashes tool always make transferred stash objects unique remove ClearStashes and ClearOrphanStashes tools Plugs use to BOUNDS draw type for plug handles fix contain issue in 2.91 TurnCorner support toggling width adjustement QuadCorner fix issue in redo panel various other tweaks and improvements add basic statusbar hints for all modal tools improve stash and HUD drawing incl. anti aliasing update stash matrix format and introduce stash versioning and UUIDs support ALT navigation in modal tools where it was still missing make HUD following mouse optional expose HUD timeout factor to preferences fix HUD issue when modal ops are called via keymap experimental features (undocumented and excluded from product support!) split edge NormalTransfer approach next to sharp edges BooleanCleanup flip option, useful for mesh cuts OffsetCut tool add MESHmachine plug update license v0.6.13 2020-09-10 fix 2.90 exception, when adding plug to library v0.6.12 2020-09-05 ensure 2.90 compatibility support ALT navigation support proper work space filtering for panels v0.6.11 2020-06-03 Symmetrize re-enable symmetrize drawing, which was left off accidentally Unfuck update widthlinked + tensionlinked icons in Redo panel prevent undo related crash bug in 2.83 fix issue with real-mirrored decals fix 2d array plugs not carrying through the cap vertex groups remove obsolete shortcut mentions from the docs v0.6.10 2019-06-27 add LSelect select loop of ngons based on initial 2 polygon selection requires loop of quads on either side select loop of edges from initial single edge selection based on angle threshold add SSelect select all connected sharp edges ViewStashes add edit stash object mode lower wire alpha LoopTools Circle Wrapper add Fix Midpoint option used for circular selections with irregular vert distribution stashes HUD offset down, if necessary fix broken decal asset libraries ui list, due to API change fix cursor wrapping in fullscreen fix array plugs not creating proper vertex group, due to API limitation (at the time) fix Xray toggles in Normal Transfer, Conform, View Stashes, etc due to change in Blender default drawing behavior fix issue in Symmetrize, when mesh is positioned in a way, that Symmetrize doesn't produce a center seam fix Examples_012 and Examples_017 array plugs fix exception when popup_message() couldn't be found, as a result of previous refactoring fix issue when removing old version and installing new one in the same Blender session prevent errors when coming across certain MM ops in Blenders operator search menu a few performance performance improvements v0.6.9 2019-05-18 Plug fix recent depsgraph issues support local view if auto smooth is required, enable it fix previous redo issues affection plug rotation and deformation AddPlugToLibrary fix recent depsgraph issues Insert clear drivers to prevent issues with plugs created in 2.79 improve raycast and raycast performance BooleanCleanup fix rare vert drawing issue stashes HUD take into account MM's modal_hud_scale and Blender's ui_scale prefs fix broken driver in Examples plug 018 fix multi-region issue with modals and HUDs fix utils.registraion.reload_plug_libraries() fix Plug Packs link in help Panel fix gumroad link in Preferences - About v0.6.8 2019-05-13 Plugs WARNING : there are issues with redoing the plug tool, until T64300 and T64307 are fixed plug rotation is disabled plug deformation doesn't work when redoing contain and normal transfer options are temporarily enabled by default for that reason create collections when bringing Plugs into the scene default to raycast based plug insertion (at the location of the mouse cursor) support local view remove show wire option and always use the fading wire instead Fuse, Unfuse, Refuse, Unchamfer, Unbevel auto-set smooth tool option based on initial selection auto-sets tool options when working on DECALmachine panel decals ChangeWidth add taper option Symmetrize, RealMirror use different colors when mirroring custom normals improve drawing performance RealMirror create collections for originals and mirrored objects VSelect draw all vgroups in addition to the highlighted and selected ones stashes HUD only draw when overlays are enabled CreateStash improve performance significantly ViewStashes support retrieving stashes in local view TransferStashes draw transferred stashes, this is especially useful in context of the re-stash option NormalTransfer optionally switch the matcap automatically when the tool is run and switch back when finished disabled NEAREAST NORMAL and NEAREST POLY NORMAL modes Remove Tape tool Grease Pencil now has a Line shape, that's similar Preferences add options for context menus for object and edit modes automatically register LoopTools, if the wrappers are enabled and LoopTools is not registered fix issues in Plug Library Rename and Remove add update check add GetSupport tool update about page remove keyboard layout selection remove options to use tools in either SIMPLE or MODAL mode add tool tips for all tools improve all modal tools prevent jumping values when cursor wrapping, when rotating the view and when toggling SHIFT and CTRL mesh scale and zoom independent modal geometry adjustments fix issues when unregistering and Loading Factory Settings start refactoring, simplifying and optimizing internals of the chamfer and fillet toolset in preparation for 0.7 v0.6 2018-10-31 Fuse only draw self.capholes in HUD, if not self.cyclic Refuse add force_projected_loop support Flatten add 1,2 scrolling Unchamfer add bweight and bweights props in draw() Unfuck() fix issue where the handle1co and handle2co wouldn't update properly when the width is changed, because the original end points were used for intersect_point_line() instead of the adjustted start1co and start2co TurnCorner set smoothing based on initially selected face QuadCorner lower width min prop only draw self.tension in HUD, if not self.single Plug and DrawPlug use utils.normal.normal_clear_across_sharps() instaed of various vertex group ops, to clear normals acress sharps hide plug type selection in draw() fix issue with cointain amount not taking into account plug and target scale MyPlugs library fix contain issue in 001_Blender plug, as a result of the latest Plug() changes CreateStash fix exception when pressing d in modal(), when stash has not been created from other object, and so sources was not defined allow d key presses to pass through, if there are no sources or if alt, shift, ctrl is pressed set MM.isstashobj prop ViewStashes when retrieving a stashe, only transfer stashes when the stash matrix == target matrix at the time the stash was created undo stash naming when retrieving a stash unset MM.isstashobj prop when retrieving TransferStashes move retrieve and restashing to utils.stash.transfer_stashes() - add prop to enable retieval and restashing useful for transfering stashes to plug subsets also useful for transfering stashes to duplicate object with applied scale/rotation ClearStashes and RemoveOrphanStashes add deletion counter title Conform make sure stashobjs matrix == active obj's matrix this means you can move and rotate an object with stashes, and conform will keep working add ViewOrphanStashes() view and retrieve orphan stashes add RemoveOrphanStashes() removes objects with MM.isstashobj prop and use_face_user props being True and users prop being 1 BooleanCleanup add poll() add 1,2 scrolling Chamfer rename Normal Transfer VGroup stuff to just Vertex Group name the actual vgroup \"chamfer\" add 1,2 scrolling Offset rename Normal Transfer VGroup stuff to just Vertex Group name the actual vgroup \"offset\" add 1,2 scrolling NormalTransfer automatically clear normals across sharp edges, if limit_by_sharps props is True experimental NormalClear change prevent_sharp_edges to limit_to_selection Symmetrize redo normal transfer without stashes Real Mirror fix parenting issues by simplifiying using matrix math Looptools Wrappers remove UP_ARROW and DOWN_ARROW VSelect add ONE and TWO keys properties.py save stash matrix in object.MM.stashmx and stash target matrix in object.MM.stashtargetmx instead of on the stashes this is necessary for the retrieval of orphan stashes remove obsolete location, rotation and scale props for stashes, it's all done via matrices now rename MM.isstash to MM.isstashobj utils.core.init_sweeps() fix \"mark loop\"/freestyle issue, where single loop edges weren't expluded when marked utils.normal.py - move normal functions from ops over here add normal_transfer_from_obj() and normal_transfer_from_stash() utils.stash.create_stash() always set the stashmx and stashtargetmx props this way, you can properly retrieve orphan stashes at the location they were created at utils.stash.transfer_stashes() add restash arg + logic utils.ui.py in draw_init() and draw_prop() support modal_hud_scale and user_preferences.view.ui_scale UI improve stash idx HUD display in NormalTransfer(), Conform(), ViewStashes and ClearStashes() remove modal HUD positioning options add modal_hud_scale prop remove adddon warning v0.5.16 limited 2018-07-13 Plug add \"global\" deformation toggle intended for cases, where you know you are working on flat geometry but the plugs are complex and have a deformer with use_deformer toggled on, perhaps even with subsets set to forcesubsetdeform you can just toggle off all deformation in that case, even for fillet plugs and speed up the plug tool considerably store handle scale store local empty location add simple subdivision to the cointainer, to avoid rare bug in negative corners, where faces to replace are found outisde the container cut InsertPlug check if plug scale is stored and set it accordingly - check if empty locations are stored and set them accordingly ValidatePlug add flipped normals check always automatically deselect handle polyons always generate unique UUIDs for emtpies, when they aren't set AddPlugToLibrary add mode selection and with it ability to replace existing plugs useful to update plugs, without having to manually remove the previous version MyPlugs plugs add Blender logo plug to previously empty library v0.5.15 limited 2018-07-04 preferences add plug creator property, useful for plug creators to attach their name, url or email or any other text string to a plug Plug re-shuffle ui in the redo panel and introduce separate deformation box - support deformers for array plugs - support deformers for subsets - expose deformer related plug precision and subset precision properties - for arrays with deformers especially, higher values seem to be necessary with increasing length of the array maintain hierarchy if subsets are paretned to other subsets make deformer usage (mesh deform instead of surface deform) optional, not mandatory, if a deformer is present, using the \"Use Deformer\" property in the redo panel - use forcesubset property, to forceably deform specific subsets, even if \"deform subsets\" is turned off in the redo panel influence deformers via the offset property in addtion to the plug and handle improve subset parenting and fix an issue, if target object is chlld of another object itself - fix context.scene.tool_settings.vertex_group_weight related issue fix issue where hook or array plug was only correctly determined if hook or array mods where present on the handle, which doest not need to be the case CreatePlug clear all materials on the plug mesh keep the subset origin and orientation, instead of aligning it to the plug SetPlugProps - optionally set the default deformer precision value for plugs and subsets add forcesubsetdeform property add isplugoccluder property AddPlugToLibrary show indicator for deformer plugs, a small d in the bottom right corner white: deformer use enabled by default black: deformer use is disabled by default missing: no deformer present support occluders, objects that help rendering icons of plugs that have significant underhangs ValidatePlug (previously DebugPlugProperties) add summary dialog there's basically no need anymore for checking the temrinal, except in cases where you want to actually debug individual plug components/objects add handle n-gon check optionally set visibility and renderability of deformer and occluders and others(array caps) if present show new plug creator property add abolity to generate new UUID (UUIDs are for future proofing and are not currently used, it's just a unique id to mark a specific plug design) Example Plugs fix plug 003 - add deformers to a few plugs - set creator property for all of them add forcesubsetdeform plug and occluder example plug - redo all icons plug_icon_render.blend update lighting witha a 3rd light source add deformer indicator v0.5.14 limited 2018-06-27 Plug polystein like detail insertion tool does not use boolean, inetead replaces faces directly unlike polystein, it does not require any special face selection either cleanup and organize expose props, as redo seems to work now add limited dissolve as a cleanup step, beofre doing tris-to-quads create deformation plane and add surface deform mod allows existing chamfers/bevels to perfectly be alligned to surface this also corrects any misalginments of the plug due to low res curvatur of the target surface this plane method and face fill function will be used as a fall back method the prefered method will be using custom grid/deformation planes per plug: these, instead of the border verts of the plug, will be used to find the faces on the target to replace they will also used for the surfac deform they shold make the current face fill obsolete and so insertions shold become less topology intrusive switch to custom per plug deformation plane system the deformation plane is called the handle and is also the parent object of the plug the plug is the mesh that is embedded the plug and the handle can have children, which will be recognized as subsets remove end in edit mode prop properly error check initial selection this could likey be made redundant with proper plug scene insertion (via DM style asset loaders) create stash when non are present and normal_transfer is True unparent subsets and plug from handle separately do plug vertex projection and handle based target face finding add handle subdivision based on precision prop move add_vgroup() and unparent() to utils.support add draw() fix issue with dissolve_limited() dissolving beyond the seection border turns out it needs to be run in FACE mode! ensure only the bridged faces and nothing else is selected before running cleanup() hide various debug output if debug is False control offset loop reach for the plug and the handle initialize precision, dissolve_angle and offset_amnt in MESHmachine menu add rotation prop, useful to finetune placement/mesh integration contain prop, used to limit the face replacement it's a bit slow, but necessary on big flat polys and long ones such as on cylinders or bevels add more vertex groups to simplify selections use bmesh to create them(assign the verts) and select verts from vgroups check for presense of conform vgroup at the beginning benchmark fix missing faces issue when contain is True fix tiny triangle at border issue when contain is True fix issue in merge where sometimes edges would be in the border and boundary group when contain is True inrease the polygon selection for normal transfer when contain is True re-format draw() create normal_transfer vgroups do it even if normal transfer prop is False add deform prop, to choose whether the plug obj is deformed diabling will only work properly with plugs, that don't have a hard edge (no bevel/fuse) if disabled a normal transfer will also not be done, as it would only smooth across the hard edge, which is undesired add deform_subsets prop, which may be desired in some cases update testing scene add sharp edge plugs do normal transfer without bpy.ops do stash creation without bpy.ops for EDGE mode, clear the normals so theres no smoothing across the hard edge caused by the data trasnfer mod add filletoredge enum prop FILLET always aplies modifier based deformation and does not do vertex proximity offsets EDGE optionally optionally oes modifier based deformation and always does vertex proximity offsets this differentiation now also allows tiny bevels to be properly deforemed, when before the vertex proximity offsets would damage them parent and unparent without bpy.ops modal, fading wire drawing of the edges relevant to the integration - the same as in the normal transver vgroup verts its drawn in a similar fashion as symmetrize, as the plug op is not a modal add fading_wire prop in draw() add support for Hooks fix redo issue in apply_hooks_and_arrays()(previously apply_hooks()) caused by lack of scene.update() add support for mesh_deform, through an additional deformer mesh it turns out the surface deform mod has some limitations, like underhangs the mod will either not bind or in some cases the bind is messy and produces offshooting verts the mesh deformer mod seems to handle these cases fine, but setting a plug with a deformer up requires additional effort if a deformer is found a mesh deform of the plug is done, by tying the deformer mesh with a surface deform to the shrinkwrapped handle add hidden deform_interpolation_falloff prop, beldner sets this value to 4, but it was insufficient for the 2 dimensional array plug increasing it to 16 seems to fix the issue and does not seem to affect other plugs negatively always add a stash if no stash is present, not just when normal transfer is checked allow plugging on plugs (before they are plugged) automatically set FILLET or EDGE on first run based on object.MM.hasfillet prop solve the vertex group issue in 2 dimensional array plugs, by first applying the array mods on the caps of the second plug array mod DrawPlug fix issue in DrawPlug which would weirdly cause props in Plug to jump when dragged in a redo panel to add to the weird, the fix was to track time handlers like the draw handlers, and remove them before creating new ones unfortunately this sometimes leads to the drawing not fading out and sticking around for a while pluggin again, toggling ao or xray or going into edit mode and out again seems to remove it this should be done properly at some point, just not sure how right now Plug Libraries add Plugs assets folder create Examples and MyPlugs libraries Examples is force-locked, won't be available for plug creation MyPlugs is the startnig library for new user plugs, it contains only 2 blender plugs CreatePlug creates plug from selected object or objects the active will be the plug mesh, any others will be subsets creates plug handle and enters EDIT mode to finish of the handle the handle mesh should be relatively evenly subdividable triangles are fine, n-gons not (as they dont subdivide) sets isplug and isplughandle object props offsets outer edges slightly sets xray and wire rendendering add uuid object prop may be useful in future add isplugsubset prop make handle invisible to render this way it doesnt need to be done for icon rendering fix issue where the handle location is not properly updated due to the hooks automatically set the object.MM.hasfillet prop on the plug object add AddPlugToLibrary allows plug library selection and optionally setting plug name figures out new plug index and builds blend and icon path save currentblend to be loaded again at the end render icon by appending the scene from plug_icon_render.blend delete everything but the plug objs and save the plug blend add indicator HUD support and props to toggle them in draw() fix issue where the handle location is not properly updated due to the hooks automatically focus viewport on handle in case you open the plug blend manually SetPlugProps manually checks and sets plug props of selected object poll whether 1 object is selected DebugPlugProps check the active objec for plug props checks the actives children as well so, ideally, you'd select the handle and run the tool, as the handle is the most parent object of a plug ClearPlugProps - useful for array plug creation when the caps of the array are created from plug meshes, that already have their props add alsoclearvgroups prop, it defaults to True Fuse save force_projected_loop prop Chamfer optionally create normal transfer vertex groups Offset optionally create normal transfer vertex group CreateStash separate out create_stash() from operator class NormalTransfer hide various debug outpuf debug is False separate out normal_transfer() and add_normal_transfer_mod() from operater class NormalClear separate out normal_clear() from operator class Real Mirror converts mirror mods into real independent objects does proper mirrored origin, orientation and custom normals add poll checking for active mirror mods fix 180 degree rotation issue allow empty targets(mirroring across itself) support multiple axes support multiple mirror mods add optional uv offset on the mirrored objects optionally create group for the mirrored objects optionally apply data transfer mods for the mirrored objects fix issue if obj received custom normals from applying data transfer in this case the loop/normal data needs to be received from the mirror object, not from the original VSelect it's like select linked for vertex groups select vert/edge/polygon run VSelect and the enite vertex group the selections belongs to will be selected if there are multiple, you can scroll through them if nothing is selected you can scroll through all of the vertex groups of the object unlike select similar set to VGROUP, this works on all selection types, and supports mulitple vgroups per selection keep it all in edit mode bmesh instead of mode switching make it modal drawHUD and drawVIEW3D individual group toggle all group toggle/invert instead of returning the vgroups common to all selected elements, return all vgroups in the selection this allows for easily selecting multiple specific groups by using on vert/edge/poly per group, coupled with the A modal toggle DrawSymmetrize get the vert ids by importing from symmetrize.py instead of passing them in as an str argumment utils.registration.py registers, unregisters and reloads plug libraries change insert function template in utils.registration to support plug removal via modal dialog operator RemovePlug() add bpy.types.Scene.newpllugidx prop automatically set bpy.context.scene.newplugidx when bpy.context.scene.pluglibs is changed utils.append.py has methods for group, world and scene appending (turned out only scene was needed for plug icon rendering) utils.devloper.Benchmark prints time for each Benchmark.meassure() prints total time via Benchmark.total() compares time to previous tool run execution add do_benchmark toggle utils.support.add_vgroup() create vgroup and make it active using bpy.ops, if no vertex id list is passed in utils.support.py add .parent() and unparent() properties.py introduce isstash, isplug and isplughandle props add isplugdeformer object property preferences add showplugcount prop add plugfadingwire prop be default this is off and so the fading wire option will not be available this is because a weird crash to desktop bug appeared it happens if you plug the only object in a scene and change the wire/fading fire options if you comment out the fading wire code, the issue vanishes, so it is related to this the weird part is, if you add a stash or a second object to the scene, verything is fine add pluglibsCOL and pluglibsIDX create Plugs tab add plugsinlibraryscale, showpluglabels and plugxraypreview prefs add plurgremovemode prop to menu add showplugbutton and showplugbuttonname props to prefs init.py add PlugLibsCollection() and PLugLibsUIList() to init.py UI add check() to all ops that have a draw function - this ensures redrawing when props change add Plug tool add Plug Libraries add plurgremovemode prop to menu create Plug Utils submenu add Create Plug, Set Plug Props, Clear Plug Props and Debug Plug Props v0.5.13 2018-06-02 Fuse add Fuse prop “Projected Loop” forces rails to not be aligned with existing loop edges add Conform tool shrink wrap with stash as target conform selection to stash object add Boolean Cleanup tool used to fix verts of an edge loop in place based on connected edges on the selected side merge the other verts based on a threshold can be used on cyclic and non-cyclic edge selections add Chamfer tool per side loop slide toggle 2 face methods: REBUILD with the optional Merge Perimeter prop REPLACE with the Reach prop the methods are different ways of dealing with geometry outside of the chamfer, which the chamfer may overlap depending on the width REBUILD should be used if the chamfer doesnt or only minimally overlaps REPLACE can be used if the chamfer overlaps a lot add Offset tool similar to Chamfer, but offsets an edge in the chosen direction add LoopTools modal wrappers for Circle and Relax only availble the LoopTools addon is activated Symmetrize() fix exception when fix center seam is turned on, but there aren’t any center verts add version string to registration terminal output v0.5.12 2018-05-22 added Merge option to Unf*ck Added Stashes (states of an object at a user set time) Create Stash from active from other(s) to active View Stashes you can also retrieve stashes Clear Stashes individual or all Transfer Stashes from other to active there’s a persistent HUD for stashes (top of the screen) shows stashes count and invalid stashes count added NormalTrasnfer tool transfers normals from a stash stash normals can be flipped from the tool’s modal using F the stash can also be smoothed if you have stashed an unsmoothed obj, using S in the modal, this has no ui representation yet, as I’m not sure what to display added Symmetrize tool it’s Blender’s symmetrize op, with the added ability to mirror custom normals default keymaps are Alt + X, Alt + Y and Alt + Y change in prefs default directions is + to - for X and Z, and - to + for Y change in prefs (unfold the keymap) when symmetrizing meshes with custom normals, Symmetrize will creates a pre-symmetrize stash this is because of the clear center seam Transfer option, you may want to use you probably want to regularly clean out those stashes if they accumulate although you don’t have to, a good strategy is probably to leave all the normal manipulation and transferig to the end, just remember to stash you model before you mess up the surfaces and you will be good MESHmachine menu: loops and normal tools have been put in sub menus stash tools are available from edit and object mode change mouse_wrap to hopefully fix a bug, that I can’t reproduce v0.5.11 2018-05-14 added Normal Flatten tool used to fix shading issues, especially for ngons, that should be flat has angle threshold value and presets only boundary faces angled below the threshold are taken into account boundary faces separated by a sharp edge are also ignored has “clear existing normals” toggle, which is as if the the Normal Clear tool were to be run before running Normal Flatten added Normal Straighten tool used to fix angular shading on straight sections of Fuse/Bevel/Bridge surfaces its effect is less noticable than Normal Flatten, but its something hat can be done in the pursuit of normal perfection added Normal Clear tool does not remove split normal data completely like Blender’ss customdata_custom_splitnormals_clear() operator does instead works on the selection only improved handling of an issue in Unfuse v0.5.10 2018-05-08 fix HUD offsets, Tape offsets and mousewrap issues with some layouts add Tape stroke undo/redo (ctrl + z/ctrl + shift + z, F1/F2) add flatten face/vert mode to subtitle in HUD QuadCorner slipped through the cracks: add mouse wrap add pen tablet support v0.5.9 2018-05-06 make Flatten, Unbvel, Unchamfer and Unfuse modals too add ability to toggle modal behavior per tool fix issue in Unbevel, in LOOP mode, if reverse was enabled, which don't show mesh split or delete in the special menu v0.5.8 2018-05-03 make Flatten, Unbvel, Unchamfer and Unfuse modals too add ability to toggle modal behavior per tool fix issue in Unbevel, in LOOP mode, if reverse was enabled, which don't show mesh split or delete in the special menu v0.5.7 2018-05-02 Fuse + Refuse fix crash to desktop bug in modal Fuse/Renfuse, when the initial run caused an exception turn off show_modal_ops (in the menu) by default UI: add viewport contolls while in a modal, by using PASSING_THROUGH when MIDDLEMOUSE is pressed v0.5.6 2018-05-01 improve error handling improve modals and HUDs rename the NEW/v0.6 handle method to FACE and the OLD/v0.5 handle method to LOOP basically, switch to LOOP when FACE fails improve modal performance fix projected_loop edge case, where the loop woud go in the wrong direction add HUD Corner position v0.5.5 2018-04-30 add Average Tension setting for Fuse/Refuse improve intersection handles (NEW/v.0.6) add modal Fuse, Refuse, QuadCorner, Unf*ck add modal HUDs add Mark/Clear Loop, these are just freestyle edges for now used to force certain edges as loop edges (if one is marked) used to exclude certain edges from loop edges (if more than one is marked) lower Unf*ck minimium vert count by one support QWERTY/QWERTZ keyboard layouts QWERTY: x key used for the MESHmachine menu XX for delete QWERTZ: y key used for the MESHmachine menu YY for mesh split modals can be turned on/off in the prefs can also be turned on/off in the MESHmachine menu, if enabled HUD position can be FIXED or FLOATING HUD color can be changed in prefs HUD hints can be turned on/off in prefs v0.5.4 2018-04-26 add ‘Modal Operators’ toggle to prefs and MESHmachine menu default ON modal Change Width mouse Left/Right for width R key for reverse (on single polygon chamfers) modal Turn Corner mouse Left/Right for width of “short side” mouse wheel up/down to select one of two corner orientations S key to set sharps (default ON) B key to set bweights (default OFF) add sharps and bweight to TurnCorner() v0.5.3 2018-04-24 * fix issue in QuadCorner caused by recently introduced intersection handles v0.5.2 2018-04-24 Unfuse properly set boundary sharps for Unfuse() add set Sharps and Beweights options to Unbevel() and Unfuse() Refuse fix bweights not being properly set based on loop edges Unbevel add the new intersection method to Unbevel set sharps when unbeveling fix exception when running ChangeWidth(), Fuse() and Unchamfer() on cyclic fuse selections fix leaving edit mode when running those same ops on non chamfer selections v0.5.1 2018-04-23 add alternative handle creation method create_intersection_handles(), based on projected loops interecting implicit faces create from the average normals of v.link_faces add alternative unchamfer() utilizing the intersection handles add unchamfer method selection make new unchamfer method primary disable force_projected in get_loops() for the new method add fallback to create_intersection_handles() which uses the old create_handles() for each failing caseA remap the average/center value used to lerp between the two handle locations in unchamfer_intersection() optionally add MESHmache menu to Specials menu add the new handle method for fuse and unfuse remove boundary rail bweigths and set sweep beweights according to biggest value of loop edges move segments > 0 check to the beginning fix issue when trying to sett beweight, while both loop edges were projected (and so no longer exist) break out of biggles_angle_loop() if the two top angles are too close together v0.5 2018-04-18 initial release", "title": "Changelog"}, {"location": "changelog.html#_1", "text": "", "title": ""}, {"location": "changelog.html#0170", "text": "2024-11-22 LSelect tool support loop selection step limit Unfuck support multiple independent selections prevent propagation when working with edge-only geometry and for multiple selections Fuse, Refuse, QuadCorner tools keep material indices (if multiple are used on a mesh) LoopTools wrappers add Space wrapper Circle wrapper fix hang when using multiple independent selections remove fix midpoint option for now Boolean tool fix TWO key not working for scrolling down add update available indication in 3D view's sidebar and in addon prefs", "title": "0.17.0"}, {"location": "changelog.html#0160", "text": "2024-07-10 CreateStash tool apply mods on stashes, depending on mode your are in stashing from object mode: apply modifiers on stash(es) keep them by holding ALT key stashing from edit mode: keep modifiers on stash apply them by holding ALT key unless you stash a specific face selection only the tools tooltip reflects these changes, so reference that if you are ever unsure re-enable fading wire drawing, accidentally left disabled in 0.15.4 BooleanApply tool properly support redoing now support individually toggling whether mods should be applied on original and operand object stashes by default mods are not applied on the original object's stash and are applied on the operand objects' stashes Symmetrize tool when not mirroring vertex groups, remove the association on the affected verts, instead of just setting the weight to 0 AddBoolean tool fix import when setting up AutoSmooth in 4.1+ Plug tool fix exception when applying normal transfer as part of the plugging addon preferences in Blender 4.2, when activating LoopTools wrappers in addon prefs, support automatically installing it from Blender extensions repo on first addon registration, try to setup the MACHIN3tools matcap_shiny_red.exr for the NormalTransfer tool, if it can be found", "title": "0.16.0"}, {"location": "changelog.html#0154", "text": "2024-05-16 bad day", "title": "0.15.4"}, {"location": "changelog.html#0153", "text": "2024-05-16 Fuse tool fix exception in BRIDGE mode, introduced accidentally in 0.15.2 Symmetrize tool when not mirroring custom normals, expose option to mirror vgroups, but default to False fix exception when mirroring custom normals, when using transfer method to fix the center seam improve HyperCursor integration Boolean tool simplify and speed up AutoSmooth setup, when toggling smoothing using S key Wedge tool improve HyperCursor integration CreateStash tool fix issues when trying to stash non-mesh objects preferences add notes for ALT + LEFTMOUSE keymap, used by default for the Select wrapper, and should be remapped for people using ALT for navigation", "title": "0.15.3"}, {"location": "changelog.html#0152", "text": "2024-04-10 Select tool/wrapper fix issue in always_loop_select mode where you can't select additional edges Boolean tool in Blender 4.1 when adding Auto Smooth mod, while existing Auto Smooth node trees are in the file, use the API which is faster! , rather than the Blender op to add the mod NormalTransfer tool prevent exception when having a selection of only sharp edges (no faces) this is rather pointless, as you really want to select faces next to sharp edges, but now it longer errors out OffsetCut tool fix rare exception when custom data layers are still present from a previous run remove Blender-auto-added Auto Smooth mods, when bringing Plugs into the scene Fuse, Refuse, Unfuse, Unchamfer, Unbevel tools support creating HyperCursor unreleased geometry gizmos Fuse, Refuse tools support maintaining vertex groups on sweep verts addon preferences update integrated updater to expose the Re-Scan button even if no update is found yet", "title": "0.15.2"}, {"location": "changelog.html#0151", "text": "2024-03-19 Boolean tool in Blender 4.1 when checking for existing Auto Smooth mods in the stack, that deviate from standard naming, support finding ones with index suffix in the node tree name", "title": "0.15.1"}, {"location": "changelog.html#015", "text": "2024-03-18 support Blender 4.1 auto smooth and custom normal related fixes in Symmetrize tool Norn<PERSON> Flatten and Straighten tools Normal Transfer tool Normal Clear tool RealMirror tool Plug tool Boolean tool Boolean tool support modifier based Auto Smooth in 4.1 when toggling (auto) smooth using S key, insert the mod towards the end of the stack, but always before Mirror and Array mods init operator's auto smooth state and angle, from active object when finishing with LMB unselect the active hide cutters from rendering via cycles visibility settings too now when canceling restore everything accordingly to its initial state flesh out statusbar Symmetrize tool expose redundant center edge removal threshold angle increase it slightly from 0 to 0.05 to be more tolerant Select tool/wrapper add Always Loop Select toggle by default connected Sharps are selected, if entire edge selection consists of sharp edges, otherwise an angle based loop selection is done with Always Loop Select enabled now, this behavior is overidden, so you don't constantly have to toggle back to loop selecting, when working on sharp edges Stashes improve how stashes HUD is drawn at the top of the 3D view properly offset HUD downs depending on region_overlap pref, theme header alpha, and header and tool header positioning furthermore offset stashes HUD down, if MACHIN3tools focus tool active, and has its own HUD drawn ViewStashes tool improve edit stash object HUD in the same way handle area and space data reference loss due to area maximizing or workspace space changes more gracefully still not recommended to do, while in edit stash mode though CreatePlug, AddPlugToLibrary tools ensure plug objects are properly render-able for the thumbnail Unchamfer tool fix typo, causing exception in debug mode handlers rework when and how handler logic is executed add asset drop cleanup handler (previously in MACHIN3tools) to automatically unlink stash objects after dropping and asset using stashes from the asset browser GetSupport tool improve the readme.html file, generated by the tool, providing more details on how to get to the system console in Bender 4.1 (and 3.5) open the readme.html in the Browser automatically addon preferences place Get Support button at top of addon prefs add custom updater NOTE: since it's only introduced now in 0.15, it will only be of use for upcoming releases, so can't be used to install this very 0.15 update yet allows for very easy addon update installation from .zip file, and from inside of Blender, instead of manually from the file browser finds matching .zip file(s) in home and Downloads folder allows selecting one of them them or manually selecting a file in any other location extracts the file to a temporary location, and installs update when quitting Blender like a manual update installation from the filebrowser, this maintains previous addon settings and custom keys see installation instructions for details supports - and defaults to - keeping the current assets to prevent accidental loss fix issue with thankyou note after manual installation without removing previous installation fix plug library unloading messages sell being shown despite registration_debug pref being disabled", "title": "0.15"}, {"location": "changelog.html#v014", "text": "2023-11-14 OffsetCut tool experimental remove dependency on face maps, as they were removed in 4.0 Wedge tool support HyperCursor 0.9.15 pre-release :) alongside previous versions addon preferences add option to control whether addon (un)registration confirmation is written to the terminal simplify and refactor how the various settings are drawn add HyperCursor to about section in addon preferences addon preferences - add new nag screen draw thank you message direction above the prefs, instead of in a popup for 5 minutes, then disappear forever fully support Blender 4.0 drop dpi arg in blf.size() support new shader names support new bevel weight custom data layer ensure modal HUDs are only drawn on active 3D view, not others fix np.float issue with newer numpy versions silence reload_modules() debug output for good", "title": "v0.14"}, {"location": "changelog.html#v013", "text": "2023-06-28 bump minimum Blender version to 3.6 Normal Transfer tool add previously removed NEAREST NORMAL and NEAREST POLY NORMAL mapping methods again they can be useful when transfering normals next to sharp edges Symmetrize tool fix issue when encountering non-manifold center edges at the center lower precision a little when determining which center edges are redundant add pre-0.6 Tutorial.blend file to 3D view's sidebar MESHmachine help panel this file contains simple test cases for the tools of the fillet tool set switch all timer modals to new system clock based method fixes a long time issue, where timer modals would sometimes run much faster than they should, which was especially annoying for the Add Boolean tool update HUD and VIEW3D drawing take system's UI scaling into account for all HUDs preferences add show_sidebar_panel toggle update about page remove bgl completely", "title": "v0.13"}, {"location": "changelog.html#v012", "text": "2022-12-30 bump minimum Blender version to 3.3 BooleanDuplicate redo completely and now duplicate the entire \"object tree\", which includes all mod objects, even those that aren't parented no longer initiate the translate tool at the end, to keep the operator props in the redo panel accessible support local view Boolean SPLIT ensure all the parenting, all (parented) mod objects, all drivers, hooks etc are proberly and separately reproduced on the split object add displace mod using a strength of 0 on the split off part change default auto smooth angle to 20 degrees instead of 30 BooleanApply remove cutter children as well Symmetrize add ability to remove redundant center edges, enabled by default only available if remove and partial are disabled, and if there are no custom normals Fuse, Change Width, etc. take object scaling into account to determine mouse sensitivity various HyperCursor integrations", "title": "v0.12"}, {"location": "changelog.html#v0112", "text": "2022-04-30 resolve odd 3.1 related crashes, hopefully add thank you note to addon preferences", "title": "v0.11.2"}, {"location": "changelog.html#v0111", "text": "2022-04-24 Boolean disable renderability for cutters a few small tweaks and fixes", "title": "v0.11.1"}, {"location": "changelog.html#v011", "text": "2022-03-09 support Blender 3.1 Plug, NormalTransfer and Conform tools use new generalized vgroup management approach, required due to changes in 3.0+ fix automatic legacy stash update", "title": "v0.11"}, {"location": "changelog.html#v010", "text": "2021-12-24 Symmetrize tool add mode to remove half the mesh instead of symmetrizing, toggle via X key add mode to symmetrize/remove only the selected parts of a mesh, toggle via S key take HUD scale prefs into account for drawing of flick distance and HUD labels BooleanDuplicate tool support recursive duplication and instancing, allowing for more more complex boolean setups, where operands themselves have boolean mods fix exception when encountering objects without a data block, such as group empties MakeUnique tool fix poll for objects without a data block, such as group empties MESHmachine menu fix is_instance poll for for non-mesh objects, such as group empties", "title": "v0.10"}, {"location": "changelog.html#v091", "text": "2021-12-18 Flatten tool support multi face flattening with Pick Shortest Path, Box or Circle Selections prevent invalid selections Unchamfer tool default to setting sharp edges only if selected faces are smooth NormalTransfer and Conform tools use temporary workaround related to this Blender 3.0 bug fix Split and Delete tools not appearing in MESHmachine's menu in Blender 3.0", "title": "v0.9.1"}, {"location": "changelog.html#v09", "text": "2021-11-25 support Blender 3.0 add BooleanDuplicate tool create instances via ALT BooleanApply tool support multi object selections Unfuse and Unbevel tools default to setting sharp edges only if selected faces are smooth improve GetSupport tool addon preferences add option for legacy line smoothing remove legacy code for Blender pre-2.93 drop bgl module use for view3d drawing (except for legacy line smoothing)", "title": "v0.9"}, {"location": "changelog.html#v082", "text": "2021-09-10 Wedge disable debug output fix issues on some linux systems", "title": "v0.8.2"}, {"location": "changelog.html#v081", "text": "2021-08-23 fix Wedge on rotated objects", "title": "v0.8.1"}, {"location": "changelog.html#v08", "text": "2021-08-22 add Wedge tool Boolean tool add SPLIT mode add MakeUnique tool useful for instanced meshes as created by Split Booleans BooleanApply tool support applying mods with instanced meshes, such as ones created by Split Booleans support applying mods whose objects are used by multiple booleans name stashes based on boolean operation this allows you to later easily filter out (and remove) stashes of the (at the time) active objects only apply mods, that are visible in the viewport ViewStashes and ViewOrphanStashes tools support setting cursor to stash Plug tool support re-targeting subset shrinkwrap mods, if their target is the plug object AddPlugToLibrary tool work around issues with linked thumbnail scene contents introduced in 2.90 Lselect tool inverse min-angle for better UX, increasing the angle now grows the selection Symmetrize tool fix issue with the flick HUD, if object origin was far behind view replace previous parenting and unparenting logic use safe matrix inversion everywhere", "title": "v0.8"}, {"location": "changelog.html#v072", "text": "2021-03-25 support stashing evaluated meshes using ALT key when swapping stash, parent active's children - including decals - to swapped stash object fix subset plug creation issue fix typos", "title": "v0.7.2"}, {"location": "changelog.html#v071", "text": "2021-03-01 add NDOF(3d mouse) support for viewport navigation in all modal tools add optional stashes HUD offset in preferences NormalTransfer and Conform tools reverse stash scroll direction, just like in the ViewStashes tool fix Quickpatch raycast error, when filtering out decals but not having DM installed fix 2.93 issues due to change in bpy.props representation before registration", "title": "v0.7.1"}, {"location": "changelog.html#v07", "text": "2021-02-20 add <PERSON>olean tool add <PERSON>oleanApply tool add Select tool a selection wrapper that picks VSelect, SSelect, LSelect or Blender's native loop select depending on existing selection context keymapped to ALT + LMB by default add QuickPatch tool add DeletePlug tool with only the handles selected, conveniently delete one or multiple plugs (incl. related and potentiall hidden deformers and occluders) use the X \"shortcut\" in the MESHmachine object menu add SweepStashes tool (sidepanel) clean up a scene after appending an object with stashes from another blend file Symmetrize tool - add flick mode symmetrize in any of the 6 local space directions using a single keymap LSelect tool support selecting multiple loops at once or one after another prevent issues with zero length edges expose Stashes in 3D view's sidepanel show, (re)name, swap and remove an object's stashes CreateStash tool support stashing face selections support finishing early and canceling ViewStashes tool add stash swapping capability support MACHIN3tools grouping, when swapping add stash clearing capability support setting wireframe alpha support canceling when stashes were retrieved select only retrieved stash objects when finishing ViewOrphanStashes tool add stash clearing capability TransferStashes tool always make transferred stash objects unique remove ClearStashes and ClearOrphanStashes tools Plugs use to BOUNDS draw type for plug handles fix contain issue in 2.91 TurnCorner support toggling width adjustement QuadCorner fix issue in redo panel various other tweaks and improvements add basic statusbar hints for all modal tools improve stash and HUD drawing incl. anti aliasing update stash matrix format and introduce stash versioning and UUIDs support ALT navigation in modal tools where it was still missing make HUD following mouse optional expose HUD timeout factor to preferences fix HUD issue when modal ops are called via keymap experimental features (undocumented and excluded from product support!) split edge NormalTransfer approach next to sharp edges BooleanCleanup flip option, useful for mesh cuts OffsetCut tool add MESHmachine plug update license", "title": "v0.7"}, {"location": "changelog.html#v0613", "text": "2020-09-10 fix 2.90 exception, when adding plug to library", "title": "v0.6.13"}, {"location": "changelog.html#v0612", "text": "2020-09-05 ensure 2.90 compatibility support ALT navigation support proper work space filtering for panels", "title": "v0.6.12"}, {"location": "changelog.html#v0611", "text": "2020-06-03 Symmetrize re-enable symmetrize drawing, which was left off accidentally Unfuck update widthlinked + tensionlinked icons in Redo panel prevent undo related crash bug in 2.83 fix issue with real-mirrored decals fix 2d array plugs not carrying through the cap vertex groups remove obsolete shortcut mentions from the docs", "title": "v0.6.11"}, {"location": "changelog.html#v0610", "text": "2019-06-27 add LSelect select loop of ngons based on initial 2 polygon selection requires loop of quads on either side select loop of edges from initial single edge selection based on angle threshold add SSelect select all connected sharp edges ViewStashes add edit stash object mode lower wire alpha LoopTools Circle Wrapper add Fix Midpoint option used for circular selections with irregular vert distribution stashes HUD offset down, if necessary fix broken decal asset libraries ui list, due to API change fix cursor wrapping in fullscreen fix array plugs not creating proper vertex group, due to API limitation (at the time) fix Xray toggles in Normal Transfer, Conform, View Stashes, etc due to change in Blender default drawing behavior fix issue in Symmetrize, when mesh is positioned in a way, that Symmetrize doesn't produce a center seam fix Examples_012 and Examples_017 array plugs fix exception when popup_message() couldn't be found, as a result of previous refactoring fix issue when removing old version and installing new one in the same Blender session prevent errors when coming across certain MM ops in Blenders operator search menu a few performance performance improvements", "title": "v0.6.10"}, {"location": "changelog.html#v069", "text": "2019-05-18 Plug fix recent depsgraph issues support local view if auto smooth is required, enable it fix previous redo issues affection plug rotation and deformation AddPlugToLibrary fix recent depsgraph issues Insert clear drivers to prevent issues with plugs created in 2.79 improve raycast and raycast performance BooleanCleanup fix rare vert drawing issue stashes HUD take into account MM's modal_hud_scale and Blender's ui_scale prefs fix broken driver in Examples plug 018 fix multi-region issue with modals and HUDs fix utils.registraion.reload_plug_libraries() fix Plug Packs link in help Panel fix gumroad link in Preferences - About", "title": "v0.6.9"}, {"location": "changelog.html#v068", "text": "2019-05-13 Plugs WARNING : there are issues with redoing the plug tool, until T64300 and T64307 are fixed plug rotation is disabled plug deformation doesn't work when redoing contain and normal transfer options are temporarily enabled by default for that reason create collections when bringing Plugs into the scene default to raycast based plug insertion (at the location of the mouse cursor) support local view remove show wire option and always use the fading wire instead <PERSON><PERSON>, Unfuse, Refuse, Unchamfer, Unbevel auto-set smooth tool option based on initial selection auto-sets tool options when working on DECALmachine panel decals ChangeWidth add taper option Symmetrize, RealMirror use different colors when mirroring custom normals improve drawing performance RealMirror create collections for originals and mirrored objects VSelect draw all vgroups in addition to the highlighted and selected ones stashes HUD only draw when overlays are enabled CreateStash improve performance significantly ViewStashes support retrieving stashes in local view TransferStashes draw transferred stashes, this is especially useful in context of the re-stash option NormalTransfer optionally switch the matcap automatically when the tool is run and switch back when finished disabled NEAREAST NORMAL and NEAREST POLY NORMAL modes Remove Tape tool Grease Pencil now has a Line shape, that's similar Preferences add options for context menus for object and edit modes automatically register LoopTools, if the wrappers are enabled and <PERSON><PERSON><PERSON><PERSON> is not registered fix issues in Plug Library Rename and Remove add update check add GetSupport tool update about page remove keyboard layout selection remove options to use tools in either SIMPLE or MODAL mode add tool tips for all tools improve all modal tools prevent jumping values when cursor wrapping, when rotating the view and when toggling SHIFT and CTRL mesh scale and zoom independent modal geometry adjustments fix issues when unregistering and Loading Factory Settings start refactoring, simplifying and optimizing internals of the chamfer and fillet toolset in preparation for 0.7", "title": "v0.6.8"}, {"location": "changelog.html#v06", "text": "2018-10-31 <PERSON><PERSON> only draw self.capholes in HUD, if not self.cyclic Refuse add force_projected_loop support Flatten add 1,2 scrolling Unchamfer add bweight and bweights props in draw() Unfuck() fix issue where the handle1co and handle2co wouldn't update properly when the width is changed, because the original end points were used for intersect_point_line() instead of the adjustted start1co and start2co TurnCorner set smoothing based on initially selected face QuadCorner lower width min prop only draw self.tension in HUD, if not self.single Plug and DrawPlug use utils.normal.normal_clear_across_sharps() instaed of various vertex group ops, to clear normals acress sharps hide plug type selection in draw() fix issue with cointain amount not taking into account plug and target scale MyPlugs library fix contain issue in 001_Blender plug, as a result of the latest Plug() changes CreateStash fix exception when pressing d in modal(), when stash has not been created from other object, and so sources was not defined allow d key presses to pass through, if there are no sources or if alt, shift, ctrl is pressed set MM.isstashobj prop ViewStashes when retrieving a stashe, only transfer stashes when the stash matrix == target matrix at the time the stash was created undo stash naming when retrieving a stash unset MM.isstashobj prop when retrieving TransferStashes move retrieve and restashing to utils.stash.transfer_stashes() - add prop to enable retieval and restashing useful for transfering stashes to plug subsets also useful for transfering stashes to duplicate object with applied scale/rotation ClearStashes and RemoveOrphanStashes add deletion counter title Conform make sure stashobjs matrix == active obj's matrix this means you can move and rotate an object with stashes, and conform will keep working add ViewOrphanStashes() view and retrieve orphan stashes add RemoveOrphanStashes() removes objects with MM.isstashobj prop and use_face_user props being True and users prop being 1 BooleanCleanup add poll() add 1,2 scrolling Chamfer rename Normal Transfer VGroup stuff to just Vertex Group name the actual vgroup \"chamfer\" add 1,2 scrolling Offset rename Normal Transfer VGroup stuff to just Vertex Group name the actual vgroup \"offset\" add 1,2 scrolling NormalTransfer automatically clear normals across sharp edges, if limit_by_sharps props is True experimental NormalClear change prevent_sharp_edges to limit_to_selection Symmetrize redo normal transfer without stashes Real Mirror fix parenting issues by simplifiying using matrix math Looptools Wrappers remove UP_ARROW and DOWN_ARROW VSelect add ONE and TWO keys properties.py save stash matrix in object.MM.stashmx and stash target matrix in object.MM.stashtargetmx instead of on the stashes this is necessary for the retrieval of orphan stashes remove obsolete location, rotation and scale props for stashes, it's all done via matrices now rename MM.isstash to MM.isstashobj utils.core.init_sweeps() fix \"mark loop\"/freestyle issue, where single loop edges weren't expluded when marked utils.normal.py - move normal functions from ops over here add normal_transfer_from_obj() and normal_transfer_from_stash() utils.stash.create_stash() always set the stashmx and stashtargetmx props this way, you can properly retrieve orphan stashes at the location they were created at utils.stash.transfer_stashes() add restash arg + logic utils.ui.py in draw_init() and draw_prop() support modal_hud_scale and user_preferences.view.ui_scale UI improve stash idx HUD display in NormalTransfer(), Conform(), ViewStashes and ClearStashes() remove modal HUD positioning options add modal_hud_scale prop remove adddon warning", "title": "v0.6"}, {"location": "changelog.html#v0516-limited", "text": "2018-07-13 Plug add \"global\" deformation toggle intended for cases, where you know you are working on flat geometry but the plugs are complex and have a deformer with use_deformer toggled on, perhaps even with subsets set to forcesubsetdeform you can just toggle off all deformation in that case, even for fillet plugs and speed up the plug tool considerably store handle scale store local empty location add simple subdivision to the cointainer, to avoid rare bug in negative corners, where faces to replace are found outisde the container cut InsertPlug check if plug scale is stored and set it accordingly - check if empty locations are stored and set them accordingly ValidatePlug add flipped normals check always automatically deselect handle polyons always generate unique UUIDs for emtpies, when they aren't set AddPlugToLibrary add mode selection and with it ability to replace existing plugs useful to update plugs, without having to manually remove the previous version MyPlugs plugs add Blender logo plug to previously empty library", "title": "v0.5.16 limited"}, {"location": "changelog.html#v0515-limited", "text": "2018-07-04 preferences add plug creator property, useful for plug creators to attach their name, url or email or any other text string to a plug Plug re-shuffle ui in the redo panel and introduce separate deformation box - support deformers for array plugs - support deformers for subsets - expose deformer related plug precision and subset precision properties - for arrays with deformers especially, higher values seem to be necessary with increasing length of the array maintain hierarchy if subsets are paretned to other subsets make deformer usage (mesh deform instead of surface deform) optional, not mandatory, if a deformer is present, using the \"Use Deformer\" property in the redo panel - use forcesubset property, to forceably deform specific subsets, even if \"deform subsets\" is turned off in the redo panel influence deformers via the offset property in addtion to the plug and handle improve subset parenting and fix an issue, if target object is chlld of another object itself - fix context.scene.tool_settings.vertex_group_weight related issue fix issue where hook or array plug was only correctly determined if hook or array mods where present on the handle, which doest not need to be the case CreatePlug clear all materials on the plug mesh keep the subset origin and orientation, instead of aligning it to the plug SetPlugProps - optionally set the default deformer precision value for plugs and subsets add forcesubsetdeform property add isplugoccluder property AddPlugToLibrary show indicator for deformer plugs, a small d in the bottom right corner white: deformer use enabled by default black: deformer use is disabled by default missing: no deformer present support occluders, objects that help rendering icons of plugs that have significant underhangs ValidatePlug (previously DebugPlugProperties) add summary dialog there's basically no need anymore for checking the temrinal, except in cases where you want to actually debug individual plug components/objects add handle n-gon check optionally set visibility and renderability of deformer and occluders and others(array caps) if present show new plug creator property add abolity to generate new UUID (UUIDs are for future proofing and are not currently used, it's just a unique id to mark a specific plug design) Example Plugs fix plug 003 - add deformers to a few plugs - set creator property for all of them add forcesubsetdeform plug and occluder example plug - redo all icons plug_icon_render.blend update lighting witha a 3rd light source add deformer indicator", "title": "v0.5.15 limited"}, {"location": "changelog.html#v0514-limited", "text": "2018-06-27 Plug polystein like detail insertion tool does not use boolean, inetead replaces faces directly unlike polystein, it does not require any special face selection either cleanup and organize expose props, as redo seems to work now add limited dissolve as a cleanup step, beofre doing tris-to-quads create deformation plane and add surface deform mod allows existing chamfers/bevels to perfectly be alligned to surface this also corrects any misalginments of the plug due to low res curvatur of the target surface this plane method and face fill function will be used as a fall back method the prefered method will be using custom grid/deformation planes per plug: these, instead of the border verts of the plug, will be used to find the faces on the target to replace they will also used for the surfac deform they shold make the current face fill obsolete and so insertions shold become less topology intrusive switch to custom per plug deformation plane system the deformation plane is called the handle and is also the parent object of the plug the plug is the mesh that is embedded the plug and the handle can have children, which will be recognized as subsets remove end in edit mode prop properly error check initial selection this could likey be made redundant with proper plug scene insertion (via DM style asset loaders) create stash when non are present and normal_transfer is True unparent subsets and plug from handle separately do plug vertex projection and handle based target face finding add handle subdivision based on precision prop move add_vgroup() and unparent() to utils.support add draw() fix issue with dissolve_limited() dissolving beyond the seection border turns out it needs to be run in FACE mode! ensure only the bridged faces and nothing else is selected before running cleanup() hide various debug output if debug is False control offset loop reach for the plug and the handle initialize precision, dissolve_angle and offset_amnt in MESHmachine menu add rotation prop, useful to finetune placement/mesh integration contain prop, used to limit the face replacement it's a bit slow, but necessary on big flat polys and long ones such as on cylinders or bevels add more vertex groups to simplify selections use bmesh to create them(assign the verts) and select verts from vgroups check for presense of conform vgroup at the beginning benchmark fix missing faces issue when contain is True fix tiny triangle at border issue when contain is True fix issue in merge where sometimes edges would be in the border and boundary group when contain is True inrease the polygon selection for normal transfer when contain is True re-format draw() create normal_transfer vgroups do it even if normal transfer prop is False add deform prop, to choose whether the plug obj is deformed diabling will only work properly with plugs, that don't have a hard edge (no bevel/fuse) if disabled a normal transfer will also not be done, as it would only smooth across the hard edge, which is undesired add deform_subsets prop, which may be desired in some cases update testing scene add sharp edge plugs do normal transfer without bpy.ops do stash creation without bpy.ops for EDGE mode, clear the normals so theres no smoothing across the hard edge caused by the data trasnfer mod add filletoredge enum prop FILLET always aplies modifier based deformation and does not do vertex proximity offsets EDGE optionally optionally oes modifier based deformation and always does vertex proximity offsets this differentiation now also allows tiny bevels to be properly deforemed, when before the vertex proximity offsets would damage them parent and unparent without bpy.ops modal, fading wire drawing of the edges relevant to the integration - the same as in the normal transver vgroup verts its drawn in a similar fashion as symmetrize, as the plug op is not a modal add fading_wire prop in draw() add support for Hooks fix redo issue in apply_hooks_and_arrays()(previously apply_hooks()) caused by lack of scene.update() add support for mesh_deform, through an additional deformer mesh it turns out the surface deform mod has some limitations, like underhangs the mod will either not bind or in some cases the bind is messy and produces offshooting verts the mesh deformer mod seems to handle these cases fine, but setting a plug with a deformer up requires additional effort if a deformer is found a mesh deform of the plug is done, by tying the deformer mesh with a surface deform to the shrinkwrapped handle add hidden deform_interpolation_falloff prop, beldner sets this value to 4, but it was insufficient for the 2 dimensional array plug increasing it to 16 seems to fix the issue and does not seem to affect other plugs negatively always add a stash if no stash is present, not just when normal transfer is checked allow plugging on plugs (before they are plugged) automatically set FILLET or EDGE on first run based on object.MM.hasfillet prop solve the vertex group issue in 2 dimensional array plugs, by first applying the array mods on the caps of the second plug array mod DrawPlug fix issue in DrawPlug which would weirdly cause props in Plug to jump when dragged in a redo panel to add to the weird, the fix was to track time handlers like the draw handlers, and remove them before creating new ones unfortunately this sometimes leads to the drawing not fading out and sticking around for a while pluggin again, toggling ao or xray or going into edit mode and out again seems to remove it this should be done properly at some point, just not sure how right now Plug Libraries add Plugs assets folder create Examples and MyPlugs libraries Examples is force-locked, won't be available for plug creation MyPlugs is the startnig library for new user plugs, it contains only 2 blender plugs CreatePlug creates plug from selected object or objects the active will be the plug mesh, any others will be subsets creates plug handle and enters EDIT mode to finish of the handle the handle mesh should be relatively evenly subdividable triangles are fine, n-gons not (as they dont subdivide) sets isplug and isplughandle object props offsets outer edges slightly sets xray and wire rendendering add uuid object prop may be useful in future add isplugsubset prop make handle invisible to render this way it doesnt need to be done for icon rendering fix issue where the handle location is not properly updated due to the hooks automatically set the object.MM.hasfillet prop on the plug object add AddPlugToLibrary allows plug library selection and optionally setting plug name figures out new plug index and builds blend and icon path save currentblend to be loaded again at the end render icon by appending the scene from plug_icon_render.blend delete everything but the plug objs and save the plug blend add indicator HUD support and props to toggle them in draw() fix issue where the handle location is not properly updated due to the hooks automatically focus viewport on handle in case you open the plug blend manually SetPlugProps manually checks and sets plug props of selected object poll whether 1 object is selected DebugPlugProps check the active objec for plug props checks the actives children as well so, ideally, you'd select the handle and run the tool, as the handle is the most parent object of a plug ClearPlugProps - useful for array plug creation when the caps of the array are created from plug meshes, that already have their props add alsoclearvgroups prop, it defaults to True Fuse save force_projected_loop prop Chamfer optionally create normal transfer vertex groups Offset optionally create normal transfer vertex group CreateStash separate out create_stash() from operator class NormalTransfer hide various debug outpuf debug is False separate out normal_transfer() and add_normal_transfer_mod() from operater class NormalClear separate out normal_clear() from operator class Real Mirror converts mirror mods into real independent objects does proper mirrored origin, orientation and custom normals add poll checking for active mirror mods fix 180 degree rotation issue allow empty targets(mirroring across itself) support multiple axes support multiple mirror mods add optional uv offset on the mirrored objects optionally create group for the mirrored objects optionally apply data transfer mods for the mirrored objects fix issue if obj received custom normals from applying data transfer in this case the loop/normal data needs to be received from the mirror object, not from the original VSelect it's like select linked for vertex groups select vert/edge/polygon run VSelect and the enite vertex group the selections belongs to will be selected if there are multiple, you can scroll through them if nothing is selected you can scroll through all of the vertex groups of the object unlike select similar set to VGROUP, this works on all selection types, and supports mulitple vgroups per selection keep it all in edit mode bmesh instead of mode switching make it modal drawHUD and drawVIEW3D individual group toggle all group toggle/invert instead of returning the vgroups common to all selected elements, return all vgroups in the selection this allows for easily selecting multiple specific groups by using on vert/edge/poly per group, coupled with the A modal toggle DrawSymmetrize get the vert ids by importing from symmetrize.py instead of passing them in as an str argumment utils.registration.py registers, unregisters and reloads plug libraries change insert function template in utils.registration to support plug removal via modal dialog operator RemovePlug() add bpy.types.Scene.newpllugidx prop automatically set bpy.context.scene.newplugidx when bpy.context.scene.pluglibs is changed utils.append.py has methods for group, world and scene appending (turned out only scene was needed for plug icon rendering) utils.devloper.Benchmark prints time for each Benchmark.meassure() prints total time via Benchmark.total() compares time to previous tool run execution add do_benchmark toggle utils.support.add_vgroup() create vgroup and make it active using bpy.ops, if no vertex id list is passed in utils.support.py add .parent() and unparent() properties.py introduce isstash, isplug and isplughandle props add isplugdeformer object property preferences add showplugcount prop add plugfadingwire prop be default this is off and so the fading wire option will not be available this is because a weird crash to desktop bug appeared it happens if you plug the only object in a scene and change the wire/fading fire options if you comment out the fading wire code, the issue vanishes, so it is related to this the weird part is, if you add a stash or a second object to the scene, verything is fine add pluglibsCOL and pluglibsIDX create Plugs tab add plugsinlibraryscale, showpluglabels and plugxraypreview prefs add plurgremovemode prop to menu add showplugbutton and showplugbuttonname props to prefs init.py add PlugLibsCollection() and PLugLibsUIList() to init.py UI add check() to all ops that have a draw function - this ensures redrawing when props change add Plug tool add Plug Libraries add plurgremovemode prop to menu create Plug Utils submenu add Create Plug, Set Plug Props, Clear Plug Props and Debug Plug Props", "title": "v0.5.14 limited"}, {"location": "changelog.html#v0513", "text": "2018-06-02 Fuse add Fuse prop “Projected Loop” forces rails to not be aligned with existing loop edges add Conform tool shrink wrap with stash as target conform selection to stash object add Boolean Cleanup tool used to fix verts of an edge loop in place based on connected edges on the selected side merge the other verts based on a threshold can be used on cyclic and non-cyclic edge selections add Cham<PERSON> tool per side loop slide toggle 2 face methods: RE<PERSON><PERSON>LD with the optional Merge Perimeter prop REPLACE with the Reach prop the methods are different ways of dealing with geometry outside of the chamfer, which the chamfer may overlap depending on the width REBUILD should be used if the chamfer doesnt or only minimally overlaps REPLACE can be used if the chamfer overlaps a lot add Offset tool similar to Chamfer, but offsets an edge in the chosen direction add LoopTools modal wrappers for Circle and Relax only availble the LoopTools addon is activated Symmetrize() fix exception when fix center seam is turned on, but there aren’t any center verts add version string to registration terminal output", "title": "v0.5.13"}, {"location": "changelog.html#v0512", "text": "2018-05-22 added Merge option to Unf*ck Added Stashes (states of an object at a user set time) Create Stash from active from other(s) to active View Stashes you can also retrieve stashes Clear Stashes individual or all Transfer Stashes from other to active there’s a persistent HUD for stashes (top of the screen) shows stashes count and invalid stashes count added NormalTrasnfer tool transfers normals from a stash stash normals can be flipped from the tool’s modal using F the stash can also be smoothed if you have stashed an unsmoothed obj, using S in the modal, this has no ui representation yet, as I’m not sure what to display added Symmetrize tool it’s Blender’s symmetrize op, with the added ability to mirror custom normals default keymaps are Alt + X, Alt + Y and Alt + Y change in prefs default directions is + to - for X and Z, and - to + for Y change in prefs (unfold the keymap) when symmetrizing meshes with custom normals, Symmetrize will creates a pre-symmetrize stash this is because of the clear center seam Transfer option, you may want to use you probably want to regularly clean out those stashes if they accumulate although you don’t have to, a good strategy is probably to leave all the normal manipulation and transferig to the end, just remember to stash you model before you mess up the surfaces and you will be good MESHmachine menu: loops and normal tools have been put in sub menus stash tools are available from edit and object mode change mouse_wrap to hopefully fix a bug, that I can’t reproduce", "title": "v0.5.12"}, {"location": "changelog.html#v0511", "text": "2018-05-14 added Normal Flatten tool used to fix shading issues, especially for ngons, that should be flat has angle threshold value and presets only boundary faces angled below the threshold are taken into account boundary faces separated by a sharp edge are also ignored has “clear existing normals” toggle, which is as if the the Normal Clear tool were to be run before running Normal Flatten added Normal Straighten tool used to fix angular shading on straight sections of Fuse/Bevel/Bridge surfaces its effect is less noticable than Normal Flatten, but its something hat can be done in the pursuit of normal perfection added Normal Clear tool does not remove split normal data completely like Blender’ss customdata_custom_splitnormals_clear() operator does instead works on the selection only improved handling of an issue in Unfuse", "title": "v0.5.11"}, {"location": "changelog.html#v0510", "text": "2018-05-08 fix HUD offsets, Tape offsets and mousewrap issues with some layouts add Tape stroke undo/redo (ctrl + z/ctrl + shift + z, F1/F2) add flatten face/vert mode to subtitle in HUD QuadCorner slipped through the cracks: add mouse wrap add pen tablet support", "title": "v0.5.10"}, {"location": "changelog.html#v059", "text": "2018-05-06 make <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Unchamfer and Unfuse modals too add ability to toggle modal behavior per tool fix issue in Unbevel, in LOOP mode, if reverse was enabled, which don't show mesh split or delete in the special menu", "title": "v0.5.9"}, {"location": "changelog.html#v058", "text": "2018-05-03 make <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>chamfer and Unfuse modals too add ability to toggle modal behavior per tool fix issue in Unbevel, in LOOP mode, if reverse was enabled, which don't show mesh split or delete in the special menu", "title": "v0.5.8"}, {"location": "changelog.html#v057", "text": "2018-05-02 Fuse + Refuse fix crash to desktop bug in modal Fuse/Renfuse, when the initial run caused an exception turn off show_modal_ops (in the menu) by default UI: add viewport contolls while in a modal, by using PASSING_THROUGH when MIDDLEMOUSE is pressed", "title": "v0.5.7"}, {"location": "changelog.html#v056", "text": "2018-05-01 improve error handling improve modals and HUDs rename the NEW/v0.6 handle method to FACE and the OLD/v0.5 handle method to LOOP basically, switch to LOOP when FACE fails improve modal performance fix projected_loop edge case, where the loop woud go in the wrong direction add HUD Corner position", "title": "v0.5.6"}, {"location": "changelog.html#v055", "text": "2018-04-30 add Average Tension setting for Fuse/Refuse improve intersection handles (NEW/v.0.6) add modal Fuse, <PERSON>fuse, QuadCorner, Unf*ck add modal HUDs add Mark/Clear Loop, these are just freestyle edges for now used to force certain edges as loop edges (if one is marked) used to exclude certain edges from loop edges (if more than one is marked) lower Unf*ck minimium vert count by one support QWERTY/QWERTZ keyboard layouts QWERTY: x key used for the MESHmachine menu XX for delete QWERTZ: y key used for the MESHmachine menu YY for mesh split modals can be turned on/off in the prefs can also be turned on/off in the MESHmachine menu, if enabled HUD position can be FIXED or FLOATING HUD color can be changed in prefs HUD hints can be turned on/off in prefs", "title": "v0.5.5"}, {"location": "changelog.html#v054", "text": "2018-04-26 add ‘Modal Operators’ toggle to prefs and MESHmachine menu default ON modal Change Width mouse Left/Right for width R key for reverse (on single polygon chamfers) modal Turn Corner mouse Left/Right for width of “short side” mouse wheel up/down to select one of two corner orientations S key to set sharps (default ON) B key to set bweights (default OFF) add sharps and bweight to TurnCorner()", "title": "v0.5.4"}, {"location": "changelog.html#v053", "text": "2018-04-24 * fix issue in QuadCorner caused by recently introduced intersection handles", "title": "v0.5.3"}, {"location": "changelog.html#v052", "text": "2018-04-24 Unfuse properly set boundary sharps for Unfuse() add set Sharps and Beweights options to <PERSON>bevel() and Unfuse() Refuse fix bweights not being properly set based on loop edges Unbevel add the new intersection method to Unbevel set sharps when unbeveling fix exception when running ChangeWidth(), <PERSON>se() and <PERSON>chamfer() on cyclic fuse selections fix leaving edit mode when running those same ops on non chamfer selections", "title": "v0.5.2"}, {"location": "changelog.html#v051", "text": "2018-04-23 add alternative handle creation method create_intersection_handles(), based on projected loops interecting implicit faces create from the average normals of v.link_faces add alternative unchamfer() utilizing the intersection handles add unchamfer method selection make new unchamfer method primary disable force_projected in get_loops() for the new method add fallback to create_intersection_handles() which uses the old create_handles() for each failing caseA remap the average/center value used to lerp between the two handle locations in unchamfer_intersection() optionally add MESHmache menu to Specials menu add the new handle method for fuse and unfuse remove boundary rail bweigths and set sweep beweights according to biggest value of loop edges move segments > 0 check to the beginning fix issue when trying to sett beweight, while both loop edges were projected (and so no longer exist) break out of biggles_angle_loop() if the two top angles are too close together", "title": "v0.5.1"}, {"location": "changelog.html#v05", "text": "2018-04-18 initial release", "title": "v0.5"}, {"location": "circle_relax.html", "text": "Circle and Relax The Circle and Relax tools are modal wrappers around Looptools Circle and Relax tools. keep in mind Looptools is an addon, supplied by default with Blender 2.79, but it needs to be activated first, before MESHmachine's wrappers are available. Selection Vertex or Edge loops. Using Circle and Relax", "title": "Circle and Relax"}, {"location": "circle_relax.html#circle-and-relax", "text": "The Circle and Relax tools are modal wrappers around Looptools Circle and Relax tools. keep in mind Looptools is an addon, supplied by default with Blender 2.79, but it needs to be activated first, before MESHmachine's wrappers are available.", "title": "Circle and Relax"}, {"location": "circle_relax.html#selection", "text": "Vertex or Edge loops.", "title": "Selection"}, {"location": "circle_relax.html#using-circle-and-relax", "text": "", "title": "Using Circle and Relax"}, {"location": "conform.html", "text": "Conform The Conform tool provides convenient access to the shrinkwrap modifier, while being in edit mode. It shrinkwraps the selected geometry to a chosen stash object . Selection Verts, edges or faces, on an object carrying stashes. Using Conform", "title": "Conform"}, {"location": "conform.html#conform", "text": "The Conform tool provides convenient access to the shrinkwrap modifier, while being in edit mode. It shrinkwraps the selected geometry to a chosen stash object .", "title": "Conform"}, {"location": "conform.html#selection", "text": "Verts, edges or faces, on an object carrying stashes.", "title": "Selection"}, {"location": "conform.html#using-conform", "text": "", "title": "Using Conform"}, {"location": "create_stash.html", "text": "Create Stash shortcut ys edit and object mode Note The Create Stash tool appears as <PERSON>ash it or Stash them in the menu. Stashes can be thought of as backups of objects. These backups can then be brought back at any time, or referenced/used by other tools, such as Conform or Normal Transfer . Stash objects do not clutter your scene, because they don't exist in any scene. They are only stored in the blend file itself. The Create Stash tool is used to create a stash, either from the currently active object, or if multiple objects are selected, to the active object. Selection One or multiple objects can be selected, one has to be active. You can stash in object and edit mode. Since version 0.7, you can also stash face selections. Using Create Stash", "title": "C<PERSON><PERSON>"}, {"location": "create_stash.html#create-stash", "text": "shortcut ys edit and object mode Note The Create Stash tool appears as <PERSON>ash it or Stash them in the menu. Stashes can be thought of as backups of objects. These backups can then be brought back at any time, or referenced/used by other tools, such as Conform or Normal Transfer . Stash objects do not clutter your scene, because they don't exist in any scene. They are only stored in the blend file itself. The Create Stash tool is used to create a stash, either from the currently active object, or if multiple objects are selected, to the active object.", "title": "C<PERSON><PERSON>"}, {"location": "create_stash.html#selection", "text": "One or multiple objects can be selected, one has to be active. You can stash in object and edit mode. Since version 0.7, you can also stash face selections.", "title": "Selection"}, {"location": "create_stash.html#using-create-stash", "text": "", "title": "Using Create Stash"}, {"location": "delete_plug.html", "text": "Delete Plug shortcut yx object mode Unlike the Remove Plugs option, which allows removing plugs from a library , the Delete Plug tool is used to remove plugs from the scene . Selection One or multiple plug handles Using Delete Plug", "title": "Delete Plug"}, {"location": "delete_plug.html#delete-plug", "text": "shortcut yx object mode Unlike the Remove Plugs option, which allows removing plugs from a library , the Delete Plug tool is used to remove plugs from the scene .", "title": "Delete Plug"}, {"location": "delete_plug.html#selection", "text": "One or multiple plug handles", "title": "Selection"}, {"location": "delete_plug.html#using-delete-plug", "text": "", "title": "Using Delete Plug"}, {"location": "faq.html", "text": "Contents Installation Get Support Other Addons Terminology Using MESHmachine Installation How is it done? See the Installation guide and note the version requirement. Will MESHmachine work in Blender 4.0-alpha (some experimental build)? Using experimental builds of Blender, you are at risk of encountering sudden failures of addons that used to work just fine the day before. I can't make any guarantees if things will keep working. Fixing things that break in experimental Blender builds will not be a priority, until the experimental build approaches release. Get support Attention Note the Requirements in the installation guide. Make sure you are using the latest version . Confirm you've followed the installation instructions . Also note, that experimental features are excluded from product support! General information To provide help, I need the following: Proof of Purchase system-info.txt Please use the Get Support tool in the help panel to create the system-info.txt file, and for further instructions. Errors If you are seeing an error, please send me a screenshot of the system console . Just an screenshot of the error message popup usually lacks context and is not ideal. Instead of sending a screenshot, you can also copy and paste the console text into the email. Keep in mind On Windows you can turn on the console via Window > Toggle System Console . On Linux and MacOS, you need start Blender from a terminal. I do not need an image of <PERSON><PERSON><PERSON>'s Info View and I don't need to see <PERSON><PERSON><PERSON>'s Python Console either. If the error only occurs on a certain model, please attach the blend file as well. Please remove any part of the model that doesn't contrinbute to the problem to keep the file size small. Tool misbehaviors If you think a tool of MESHmachine doesn't do what it should do, please send me the blend file . Please remove any part of the model that doesn't contribute to the problem to keep the file size small. Contact Use eMail , not twitter, not facebook, not youtube, not artstation, not blender market, and not the Blender Artists or polycount threads for reporting of errors . Other Addons Are DECALmachine and MACHIN3tools required? No, MESHmachine provides modeling tools that work on the mesh level. It does not depend on any other addon to do so. MESHmachine is a mesh modeling toolset, while DECALmachine is a detailing toolset, using mesh decals. MACHIN3tools is a free addon collection, covering a variety of tasks simple, common blender task, including but not exclusively related to modeling. Is HardOps or BoxCutter required? HardOps and BoxCutter are not required to use MESHmachine. They can complement each other well however, and I personally do use both of them. Terminology What is a chamfer? What is a fillet? What is a bevel? A chamfer is a transitional surface between 2 other surfaces, often - but not necessiarily - at a 45 degree angle. It's also referred to as a flat bevel. In blender it can be created using the Bevel tool and modifier, if the segments are set to 1 and with the Bridge tool if the number of cuts is set to 0. A chamfer has a flat profile . Similarly, a fillet is a transitional surface, with a curved profile . It's also referred to as a rounded bevel. In blender it can be created using the Bevel tool and modifier, if the segments are set to higher than 1 and with the Bridge tool if the number of cuts is set to above 0. A fillet has a round profile . Both are used to refine and replace hard edges. this chamfer has 2 micro bevels of its own! And so in the context of MESHmachine, I think it makes sense to differentiate between fillet and bevel. Bevel is a tool to create a fillet from a hard edge. And with MESHmachine's fuse tool, you can now also create a fillet from a chamfer. The chamfer itself, can of course also be created by the bevel tool or by using MESHmachine's Chamfer tool, although the latter is specifically made for post-boolean chamfers. What are rail and sweep edges? Rail edges are edges or edgeloops going along a bevel or chamfer. Sweep edges are edges going across. chamfers have only 2 sets of rails while bevels can have many rails Rail edges are great to loop select across a chamfer or bevel, while sweep edges can be used to loop select along a chamfer or bevel. Loop Selections Loop selections are done - by default via Alt + Select Mouse , while pointing at an edge. What are triangular and quad bevel corners? A triangular bevel corner is one, that has three corner vertices. Similarly a quad corner is one, that has four corner verts. a triangular bevel corner can be a single triangle, can include a single triangle in its center or may not contain a triangle at all Triangular Bevel Corners Triangular bevel corners split the flow of a bevel/fillet in 2 directions. This is undesired and tools like Unfuse , Refuse and Unbevel , will not work with these kinds of corners. The Quad Corner tool can be used to convert triangular bevel corners to quad corners. Is this non-destructive modeling? It's not non-destructive , no. With MESHmachine you work directly on the mesh level. There's no continuous construction history as in CAD modeling tools, and there aren't stacks of modifiers either. Mesh manipulation is permanent. What MESHmachine's chamfer and fillet toolset does, is reconstruct geometry, which is imperfect, but often good enough. And so, I'd call it pseudo-non-destructive, or better reconstructive . Using MESHmachine Where can I discuss the MESHmachine workflow? I'd invite you to discuss all things MESHmachine (except errors and tool misbehaviors ) in the blenderartists thread. Can I use this with subdivision surfaces? Are plugs compatible with sub-d's? Subdivision Surface modeling is generally understood as working on a low to mid poly model, and adding a live sub-d modfier on top, which produces a higher density, smoother mesh. While I think some of MESHmachine's tools can be helpful in this context, it's not really aimed at that. You will likely have to manually remove ngons and avoid triangles due to the tight topology constraints of the sub-d workflow. For me personally, sub-d's only play a role, if a sculptural surfacing is an important part of the form language. And in that case I will use it to generate the main forms, but then fatten the modifier stack. In that scenario you can then abolutely use plugs on top of the mesh, but you loose the flexibily of sub-d modeling. What you can't do is use plugs on the low or mid poly model while keeping the sub-d modifier live. This is asking for trouble. So, if you use sub-d's as a finishing tool, MESHmachine is not your best choice. If you only use it for form generation initialy and are willing to apply the subsurf modifier, MESHmachine can be useful. Why is the ... tool greyed out in the MESHmachine menu? Every time you open the MESHmachine menu , each tool listed will check if certain conditions are met, before becoming available, a process called polling. This is to avoid having to deal with illegal selections a tool can't properly process. Refer to the individual tool pages here in the docs, for details on what tool expects what kind of selection. Note These conditions are intentionally very simple and just because, a tool is no longer greyed out does not mean it will execute properly. Illegal selections may still be present and you will receive feedback, if that's the case. How can I get the Plug popup? How can I adjust a tool's properties afterwards? In Blender the result of most tools, can be adjusted, after a tool has been called. In case of MESHmachine's Plug tool for instance, you can set various properties to modify the way the plug geometry is integrated into a surface. There are 3 ways to do this: 1. Panel the operator properties panel at the bottom left of the screen 2. Menu the Adjust Last Operation entry in the Edit Menu 3. Popup Redo Last popup called via a shortcut the default keymap is F9 or F6 depending on your chosen keymap If you like using the popup, consider mapping it to a button on your mouse, which is how I do it in all feature videos.", "title": "FAQ"}, {"location": "faq.html#contents", "text": "Installation Get Support Other Addons Terminology Using MESHmachine", "title": "Contents"}, {"location": "faq.html#installation", "text": "", "title": "Installation"}, {"location": "faq.html#how-is-it-done", "text": "See the Installation guide and note the version requirement.", "title": "How is it done?"}, {"location": "faq.html#will-meshmachine-work-in-blender-40-alpha-some-experimental-build", "text": "Using experimental builds of Blender, you are at risk of encountering sudden failures of addons that used to work just fine the day before. I can't make any guarantees if things will keep working. Fixing things that break in experimental Blender builds will not be a priority, until the experimental build approaches release.", "title": "Will MESHmachine work in Blender 4.0-alpha (some experimental build)?"}, {"location": "faq.html#get-support", "text": "Attention Note the Requirements in the installation guide. Make sure you are using the latest version . Confirm you've followed the installation instructions . Also note, that experimental features are excluded from product support!", "title": "Get support"}, {"location": "faq.html#general-information", "text": "To provide help, I need the following: Proof of Purchase system-info.txt Please use the Get Support tool in the help panel to create the system-info.txt file, and for further instructions.", "title": "General information"}, {"location": "faq.html#errors", "text": "If you are seeing an error, please send me a screenshot of the system console . Just an screenshot of the error message popup usually lacks context and is not ideal. Instead of sending a screenshot, you can also copy and paste the console text into the email. Keep in mind On Windows you can turn on the console via Window > Toggle System Console . On Linux and MacOS, you need start <PERSON><PERSON><PERSON> from a terminal. I do not need an image of <PERSON>lender's Info View and I don't need to see Blender's Python Console either. If the error only occurs on a certain model, please attach the blend file as well. Please remove any part of the model that doesn't contrinbute to the problem to keep the file size small.", "title": "Errors"}, {"location": "faq.html#tool-misbehaviors", "text": "If you think a tool of MESHmachine doesn't do what it should do, please send me the blend file . Please remove any part of the model that doesn't contribute to the problem to keep the file size small.", "title": "Tool misbehaviors"}, {"location": "faq.html#contact", "text": "Use eMail , not twitter, not facebook, not youtube, not artstation, not blender market, and not the Blender Artists or polycount threads for reporting of errors .", "title": "Contact"}, {"location": "faq.html#other-addons", "text": "", "title": "Other Addons"}, {"location": "faq.html#are-decalmachine-and-machin3tools-required", "text": "No, MESHmachine provides modeling tools that work on the mesh level. It does not depend on any other addon to do so. MESHmachine is a mesh modeling toolset, while DECALmachine is a detailing toolset, using mesh decals. MACHIN3tools is a free addon collection, covering a variety of tasks simple, common blender task, including but not exclusively related to modeling.", "title": "Are DECALmachine and MACHIN3tools required?"}, {"location": "faq.html#is-hardops-or-boxcutter-required", "text": "HardOps and BoxCutter are not required to use MESHmachine. They can complement each other well however, and I personally do use both of them.", "title": "Is HardOps or BoxCutter required?"}, {"location": "faq.html#terminology", "text": "", "title": "Terminology"}, {"location": "faq.html#what-is-a-chamfer-what-is-a-fillet-what-is-a-bevel", "text": "A chamfer is a transitional surface between 2 other surfaces, often - but not necessiarily - at a 45 degree angle. It's also referred to as a flat bevel. In blender it can be created using the Bevel tool and modifier, if the segments are set to 1 and with the Bridge tool if the number of cuts is set to 0. A chamfer has a flat profile . Similarly, a fillet is a transitional surface, with a curved profile . It's also referred to as a rounded bevel. In blender it can be created using the Bevel tool and modifier, if the segments are set to higher than 1 and with the Bridge tool if the number of cuts is set to above 0. A fillet has a round profile . Both are used to refine and replace hard edges. this chamfer has 2 micro bevels of its own! And so in the context of MESHmachine, I think it makes sense to differentiate between fillet and bevel. Bevel is a tool to create a fillet from a hard edge. And with MESHmachine's fuse tool, you can now also create a fillet from a chamfer. The chamfer itself, can of course also be created by the bevel tool or by using MESHmachine's Chamfer tool, although the latter is specifically made for post-boolean chamfers.", "title": "What is a chamfer? What is a fillet? What is a bevel?"}, {"location": "faq.html#what-are-rail-and-sweep-edges", "text": "Rail edges are edges or edgeloops going along a bevel or chamfer. Sweep edges are edges going across. chamfers have only 2 sets of rails while bevels can have many rails Rail edges are great to loop select across a chamfer or bevel, while sweep edges can be used to loop select along a chamfer or bevel. Loop Selections Loop selections are done - by default via Alt + Select Mouse , while pointing at an edge.", "title": "What are rail and sweep edges?"}, {"location": "faq.html#what-are-triangular-and-quad-bevel-corners", "text": "A triangular bevel corner is one, that has three corner vertices. Similarly a quad corner is one, that has four corner verts. a triangular bevel corner can be a single triangle, can include a single triangle in its center or may not contain a triangle at all Triangular Bevel Corners Triangular bevel corners split the flow of a bevel/fillet in 2 directions. This is undesired and tools like Unfuse , Refuse and Unbevel , will not work with these kinds of corners. The Quad Corner tool can be used to convert triangular bevel corners to quad corners.", "title": "What are triangular and quad bevel corners?"}, {"location": "faq.html#is-this-non-destructive-modeling", "text": "It's not non-destructive , no. With MESHmachine you work directly on the mesh level. There's no continuous construction history as in CAD modeling tools, and there aren't stacks of modifiers either. Mesh manipulation is permanent. What MESHmachine's chamfer and fillet toolset does, is reconstruct geometry, which is imperfect, but often good enough. And so, I'd call it pseudo-non-destructive, or better reconstructive .", "title": "Is this non-destructive modeling?"}, {"location": "faq.html#using-meshmachine", "text": "", "title": "Using MESHmachine"}, {"location": "faq.html#where-can-i-discuss-the-meshmachine-workflow", "text": "I'd invite you to discuss all things MESHmachine (except errors and tool misbehaviors ) in the blenderartists thread.", "title": "Where can I discuss the MESHmachine workflow?"}, {"location": "faq.html#can-i-use-this-with-subdivision-surfaces-are-plugs-compatible-with-sub-ds", "text": "Subdivision Surface modeling is generally understood as working on a low to mid poly model, and adding a live sub-d modfier on top, which produces a higher density, smoother mesh. While I think some of MESHmachine's tools can be helpful in this context, it's not really aimed at that. You will likely have to manually remove ngons and avoid triangles due to the tight topology constraints of the sub-d workflow. For me personally, sub-d's only play a role, if a sculptural surfacing is an important part of the form language. And in that case I will use it to generate the main forms, but then fatten the modifier stack. In that scenario you can then abolutely use plugs on top of the mesh, but you loose the flexibily of sub-d modeling. What you can't do is use plugs on the low or mid poly model while keeping the sub-d modifier live. This is asking for trouble. So, if you use sub-d's as a finishing tool, MESHmachine is not your best choice. If you only use it for form generation initialy and are willing to apply the subsurf modifier, MESHmachine can be useful.", "title": "Can I use this with subdivision surfaces? Are plugs compatible with sub-d's?"}, {"location": "faq.html#why-is-the-tool-greyed-out-in-the-meshmachine-menu", "text": "Every time you open the MESHmachine menu , each tool listed will check if certain conditions are met, before becoming available, a process called polling. This is to avoid having to deal with illegal selections a tool can't properly process. Refer to the individual tool pages here in the docs, for details on what tool expects what kind of selection. Note These conditions are intentionally very simple and just because, a tool is no longer greyed out does not mean it will execute properly. Illegal selections may still be present and you will receive feedback, if that's the case.", "title": "Why is the ... tool greyed out in the MESHmachine menu?"}, {"location": "faq.html#how-can-i-get-the-plug-popup-how-can-i-adjust-a-tools-properties-afterwards", "text": "In Blender the result of most tools, can be adjusted, after a tool has been called. In case of MESHmachine's Plug tool for instance, you can set various properties to modify the way the plug geometry is integrated into a surface. There are 3 ways to do this:", "title": "How can I get the Plug popup? How can I adjust a tool's properties afterwards?"}, {"location": "faq.html#1-panel", "text": "the operator properties panel at the bottom left of the screen", "title": "1. Panel"}, {"location": "faq.html#2-menu", "text": "the Adjust Last Operation entry in the Edit Menu", "title": "2. <PERSON><PERSON>"}, {"location": "faq.html#3-popup", "text": "Redo Last popup called via a shortcut the default keymap is F9 or F6 depending on your chosen keymap If you like using the popup, consider mapping it to a button on your mouse, which is how I do it in all feature videos.", "title": "3. <PERSON><PERSON>"}, {"location": "flatten.html", "text": "Flatten shortcut ye edit mode Using the Flatten tool you can flatten a single or multiple Polygons and you can flatten in 2 modes: Along Edge or Along Normal . Selection Flatten can take 2 kinds of selections. You can either select 3 vertices or multiple polygons with one being active . If you select 3 vertices, all verts of the connected polygon will be flattend based on the normal of the implicit triangle the 3 verts create. If you select 2 or poly polygons, all polygons will be flattened based on the normal of the active (last selected) polygon. 3 vert selection and 2+ polygon selection Using Flatten", "title": "<PERSON><PERSON>"}, {"location": "flatten.html#flatten", "text": "shortcut ye edit mode Using the Flatten tool you can flatten a single or multiple Polygons and you can flatten in 2 modes: Along Edge or Along Normal .", "title": "<PERSON><PERSON>"}, {"location": "flatten.html#selection", "text": "Flatten can take 2 kinds of selections. You can either select 3 vertices or multiple polygons with one being active . If you select 3 vertices, all verts of the connected polygon will be flattend based on the normal of the implicit triangle the 3 verts create. If you select 2 or poly polygons, all polygons will be flattened based on the normal of the active (last selected) polygon. 3 vert selection and 2+ polygon selection", "title": "Selection"}, {"location": "flatten.html#using-flatten", "text": "", "title": "Using Flatten"}, {"location": "fuse.html", "text": "Fuse shortcut yf edit mode The Fuse tool is the center piece of MESHmachine's chamfer and fillet toolset. Its purpose is to create rounded surfaces from chamfers/flat bevels. One of the benefits of this, is the ability to create variable fillets, as well as washouts. fillets with changing 'radii' on the left, fillets washing out on the right Selection Poly strip - a row of polygons - along the chamfer. The best way to select a chamfer is usually by loop selecting it - by default via Alt + Select Mouse , while pointing at an sweep edge of the chamfer. poly strip selection along chamfer The sequence of the chamfer polygons determine the direction of the fuse surface. If Fuse is run on a single polygon, the direction will be determined by edge length, but can can also be reversed in the redo/tool properties panel. Using Fuse", "title": "<PERSON><PERSON>"}, {"location": "fuse.html#fuse", "text": "shortcut yf edit mode The Fuse tool is the center piece of MESHmachine's chamfer and fillet toolset. Its purpose is to create rounded surfaces from chamfers/flat bevels. One of the benefits of this, is the ability to create variable fillets, as well as washouts. fillets with changing 'radii' on the left, fillets washing out on the right", "title": "<PERSON><PERSON>"}, {"location": "fuse.html#selection", "text": "Poly strip - a row of polygons - along the chamfer. The best way to select a chamfer is usually by loop selecting it - by default via Alt + Select Mouse , while pointing at an sweep edge of the chamfer. poly strip selection along chamfer The sequence of the chamfer polygons determine the direction of the fuse surface. If Fuse is run on a single polygon, the direction will be determined by edge length, but can can also be reversed in the redo/tool properties panel.", "title": "Selection"}, {"location": "fuse.html#using-fuse", "text": "", "title": "Using Fuse"}, {"location": "installation.html", "text": "Requirements Windows, MacOS, Linux Blender 3.6 LTS - 4.3 LTS Experimental Builds Experimental Blender builds such as 4.4-alpha are not supported , and fixing any issues related to them, will not be a priority, but reporting them is still encouraged. Blender on MacOS MacOS users should install Blender properly, by following the official instructions . Avoid running it just from the Downloads folder! Note that, for dragging of files and folders, you need to hold down the COMMAND key. This will ensure AppTranslocation 1 is avoided. Blender on Arch Linux Arch Linux users and users of other Arch based or similar rolling release distros are advised to use the official Blender builds . The Blender package in the Community repository does not supply its own Python, and does not follow official recommendations . As a consequence, the system's python version may not work with MESHmachine. Latest MESHmachine The latest version of MESHmachine is 0.17 - available on Gumroad and Blender Market . See this page the learn what's new in the latest versions, or see the changelog for the full release history. Installation 1. Fresh Installation NO previous version of MESHmachine installed 2. Update Installation previous version of MESHmachine installed already Attention The video below applies to updating from MESHmachine 0.15.0 (or later). If you are updating from an earlier version of MESHmachine, that doesn't have the Integrated Updater yet, please see these legacy update instructions . Keep in mind Blender 4.2 supports installation of addons by dropping a .zip file on Blender. This works great generally, but note, that for update installations: You will still need to restart Blender afterwards. You will definitely loose all custom created plug assets , if you have them in the addon's folder, instead of moving them to an external path. Learn more about AppTranslocation . ↩", "title": "Installation"}, {"location": "installation.html#_1", "text": "", "title": ""}, {"location": "installation.html#requirements", "text": "Windows, MacOS, Linux Blender 3.6 LTS - 4.3 LTS Experimental Builds Experimental Blender builds such as 4.4-alpha are not supported , and fixing any issues related to them, will not be a priority, but reporting them is still encouraged.", "title": "Requirements"}, {"location": "installation.html#blender-on-macos", "text": "MacOS users should install Blender properly, by following the official instructions . Avoid running it just from the Downloads folder! Note that, for dragging of files and folders, you need to hold down the COMMAND key. This will ensure AppTranslocation 1 is avoided.", "title": "Blender on MacOS"}, {"location": "installation.html#blender-on-arch-linux", "text": "Arch Linux users and users of other Arch based or similar rolling release distros are advised to use the official Blender builds . The Blender package in the Community repository does not supply its own Python, and does not follow official recommendations . As a consequence, the system's python version may not work with MESHmachine.", "title": "Blender on Arch Linux"}, {"location": "installation.html#latest-meshmachine", "text": "The latest version of MESHmachine is 0.17 - available on Gumroad and Blender Market . See this page the learn what's new in the latest versions, or see the changelog for the full release history.", "title": "Latest MESHmachine"}, {"location": "installation.html#installation", "text": "", "title": "Installation"}, {"location": "installation.html#1-fresh-installation", "text": "NO previous version of MESHmachine installed", "title": "1. Fresh Installation"}, {"location": "installation.html#2-update-installation", "text": "previous version of MESHmachine installed already Attention The video below applies to updating from MESHmachine 0.15.0 (or later). If you are updating from an earlier version of MESHmachine, that doesn't have the Integrated Updater yet, please see these legacy update instructions . Keep in mind Blender 4.2 supports installation of addons by dropping a .zip file on Blender. This works great generally, but note, that for update installations: You will still need to restart Blender afterwards. You will definitely loose all custom created plug assets , if you have them in the addon's folder, instead of moving them to an external path. Learn more about AppTranslocation . ↩", "title": "2. Update Installation"}, {"location": "legacy_update_installation.html", "text": "Requirements Plug Backup Attention If you are updating from a previous version, you are at risk of loosing any Plugs you may have created. If you have not chosen a plug assets location outside the MESHmachine folder in <PERSON><PERSON><PERSON>'s addons folder , you should backup your plugs as described in the video, and outlined below. Addons Folder (in user scripts) Linux : /home/<USER>/.config/blender/3.6/scripts/addons MacOS : /Users/<USER>/Library/Application Support/Blender/3.6/scripts/addons Windows : C:\\Users\\<USER>\\AppData\\Roaming\\Blender Foundation\\Blender\\3.6\\scripts\\addons Update Installation previous version of MESHmachine installed already Update Installation in the File Browser Attention Never install MESHmachine in <PERSON><PERSON><PERSON>'s program folder. On Windows, that would be C:\\Program Files\\... MESHmachine needs write access to its installation folder, so you need to install it into <PERSON><PERSON>der's addons folder . with <PERSON><PERSON><PERSON> closed, navigate to <PERSON><PERSON><PERSON>'s addons folder find MESHmachine, and copy the MESHmachine/assets/Plugs folder to a save location to backup any custom Plugs you may have created remove the MESHmachine folder in your Downloads location, extract the MESHmachine_0.15.2.zip file copy the MESHmachine folder from the zip file to <PERSON><PERSON><PERSON>'s addons folder note, if you have a MESHmachine_0.15.2 folder after extraction, don't copy this one, instead copy the MESHmachine folder contained in it start Blender and check if the menu ( Y key) comes up Update Installation in Blender start Blender, bring up preferences switch to the Add-ons tab and use the search input at the top right to find your currently installed MESHmachine version unfold MESHmachine, go to the Plugs tab, and shift click on the folder icon of the assets path in your filebrowser copy any custom plugs you may have to a safe location back Blender, first deactivate MESHmachine, then click the Remove button with MM uninstalled, click the Install... button at the top right locate the downloaded MESHmachine_0.15.2.zip file and double-click it activate the addon by ticking the checkbox ensure your preferences are saved (by default done automatically) close preferences and in the 3D View press the Y key to bring up the MESHmachine menu", "title": "Legacy update installation"}, {"location": "legacy_update_installation.html#_1", "text": "", "title": ""}, {"location": "legacy_update_installation.html#requirements", "text": "", "title": "Requirements"}, {"location": "legacy_update_installation.html#plug-backup", "text": "Attention If you are updating from a previous version, you are at risk of loosing any Plugs you may have created. If you have not chosen a plug assets location outside the MESHmachine folder in Blender's addons folder , you should backup your plugs as described in the video, and outlined below. Addons Folder (in user scripts) Linux : /home/<USER>/.config/blender/3.6/scripts/addons MacOS : /Users/<USER>/Library/Application Support/Blender/3.6/scripts/addons Windows : C:\\Users\\<USER>\\AppData\\Roaming\\Blender Foundation\\Blender\\3.6\\scripts\\addons", "title": "Plug Backup"}, {"location": "legacy_update_installation.html#update-installation", "text": "previous version of MESHmachine installed already", "title": "Update Installation"}, {"location": "legacy_update_installation.html#update-installation-in-the-file-browser", "text": "Attention Never install MESHmachine in <PERSON><PERSON><PERSON>'s program folder. On Windows, that would be C:\\Program Files\\... MESHmachine needs write access to its installation folder, so you need to install it into <PERSON><PERSON><PERSON>'s addons folder . with <PERSON><PERSON><PERSON> closed, navigate to <PERSON><PERSON><PERSON>'s addons folder find MESHmachine, and copy the MESHmachine/assets/Plugs folder to a save location to backup any custom Plugs you may have created remove the MESHmachine folder in your Downloads location, extract the MESHmachine_0.15.2.zip file copy the MESHmachine folder from the zip file to <PERSON><PERSON><PERSON>'s addons folder note, if you have a MESHmachine_0.15.2 folder after extraction, don't copy this one, instead copy the MESHmachine folder contained in it start <PERSON><PERSON><PERSON> and check if the menu ( Y key) comes up", "title": "Update Installation in the File Browser"}, {"location": "legacy_update_installation.html#update-installation-in-blender", "text": "start Blender, bring up preferences switch to the Add-ons tab and use the search input at the top right to find your currently installed MESHmachine version unfold MESHmachine, go to the Plugs tab, and shift click on the folder icon of the assets path in your filebrowser copy any custom plugs you may have to a safe location back <PERSON>lender, first deactivate MESHmachine, then click the Remove button with MM uninstalled, click the Install... button at the top right locate the downloaded MESHmachine_0.15.2.zip file and double-click it activate the addon by ticking the checkbox ensure your preferences are saved (by default done automatically) close preferences and in the 3D View press the Y key to bring up the MESHmachine menu", "title": "Update Installation in Blender"}, {"location": "lselect.html", "text": "LSelect The LSelect tool, found in the Select sub-menu, is used to loop select edges based on a minimum angle parameter. This can produce much better results compared to Blender's native loop select, which is aimed at loop selecting all-quad topologies. Also see the Select tool, for a more convenient way to access LSelect . Selection One or multiple isolated selected edges. Using LSelect Keep in mind The Min Angle behavior shown in the video has been inverted in MESHmachine 0.8, leading to a better UX. Increasing the value will now grow the selection.", "title": "LSelect"}, {"location": "lselect.html#lselect", "text": "The LSelect tool, found in the Select sub-menu, is used to loop select edges based on a minimum angle parameter. This can produce much better results compared to Blender's native loop select, which is aimed at loop selecting all-quad topologies. Also see the Select tool, for a more convenient way to access LSelect .", "title": "LSelect"}, {"location": "lselect.html#selection", "text": "One or multiple isolated selected edges.", "title": "Selection"}, {"location": "lselect.html#using-lselect", "text": "Keep in mind The Min Angle behavior shown in the video has been inverted in MESHmachine 0.8, leading to a better UX. Increasing the value will now grow the selection.", "title": "Using LSelect"}, {"location": "mark_loop.html", "text": "Mark Loop The Mark Loop tool can be used to add fine grained control to the Fuse tool. A marked edge, will either forcibly be used to guide a fuse surface, or will be ignored, depending on the presence of other edges. Note Marking loop edges currently just uses Blender's ability to mark freestyle edges. In the future, I want to remove this tool and create custom manipulators for the fuse tool, which allow for more interactive loop edge manipulation. Using Mark Loop Clear Loop Use Clear Loop to 'unmark' loop edges.", "title": "Mark <PERSON>"}, {"location": "mark_loop.html#mark-loop", "text": "The Mark Loop tool can be used to add fine grained control to the Fuse tool. A marked edge, will either forcibly be used to guide a fuse surface, or will be ignored, depending on the presence of other edges. Note Marking loop edges currently just uses Blender's ability to mark freestyle edges. In the future, I want to remove this tool and create custom manipulators for the fuse tool, which allow for more interactive loop edge manipulation.", "title": "Mark <PERSON>"}, {"location": "mark_loop.html#using-mark-loop", "text": "", "title": "Using Mark <PERSON>"}, {"location": "mark_loop.html#clear-loop", "text": "Use Clear Loop to 'unmark' loop edges.", "title": "Clear Loop"}, {"location": "normal_clear.html", "text": "Normal Clear The Normal Clear tool can be used to selectively clear custom normals. This is different from Blenders Clear Custom Split Normals Data which clears the entire model. Selection Faces, edges or verts with custom noamls. Using Normal Clear", "title": "Normal Clear"}, {"location": "normal_clear.html#normal-clear", "text": "The Normal Clear tool can be used to selectively clear custom normals. This is different from Blenders Clear Custom Split Normals Data which clears the entire model.", "title": "Normal Clear"}, {"location": "normal_clear.html#selection", "text": "Faces, edges or verts with custom noamls.", "title": "Selection"}, {"location": "normal_clear.html#using-normal-clear", "text": "", "title": "Using Normal Clear"}, {"location": "normal_flatten_straighten.html", "text": "Normal Flatten and Straighten The Normal Flatten tool is similar to Blender's Set Normals from Faces tool. Both set normals based on face selections, and so 'flatten' the shading. The difference is Normal Flatten respects the presence of sharp edges and can also be used with an angle threshold. left: before flattening, right: after flattening Selection One or multiple faces. The faces should be flat geometrically for best results. The Normal Straighten tool is used to straigthen out the shading on straight fillet sections or cylindrical or conical meshes. left: before straightening, right: after straightening Selection Polystrip across straight fillet or cylindrical sections. You can select multiple islands the the same time, but they shouldn't be directly next to each other. Using Normal Flatten and Straighten", "title": "Normal Flatten and Straighten"}, {"location": "normal_flatten_straighten.html#normal-flatten-and-straighten", "text": "The Normal Flatten tool is similar to Blender's Set Normals from Faces tool. Both set normals based on face selections, and so 'flatten' the shading. The difference is Normal Flatten respects the presence of sharp edges and can also be used with an angle threshold. left: before flattening, right: after flattening", "title": "Normal Flatten and Straighten"}, {"location": "normal_flatten_straighten.html#selection", "text": "One or multiple faces. The faces should be flat geometrically for best results. The Normal Straighten tool is used to straigthen out the shading on straight fillet sections or cylindrical or conical meshes. left: before straightening, right: after straightening", "title": "Selection"}, {"location": "normal_flatten_straighten.html#selection_1", "text": "Polystrip across straight fillet or cylindrical sections. You can select multiple islands the the same time, but they shouldn't be directly next to each other.", "title": "Selection"}, {"location": "normal_flatten_straighten.html#using-normal-flatten-and-straighten", "text": "", "title": "Using Normal Flatten and Straighten"}, {"location": "normal_transfer.html", "text": "Normal Transfer The Normal Transfer tool provides convenient access to the data transfer modifier, while being in edit mode. It transfers normals from the chosen stash object to the selected geometry. Selection Verts, edges or faces, on an object carrying stashes. Using Normal Transfer", "title": "Normal Transfer"}, {"location": "normal_transfer.html#normal-transfer", "text": "The Normal Transfer tool provides convenient access to the data transfer modifier, while being in edit mode. It transfers normals from the chosen stash object to the selected geometry.", "title": "Normal Transfer"}, {"location": "normal_transfer.html#selection", "text": "Verts, edges or faces, on an object carrying stashes.", "title": "Selection"}, {"location": "normal_transfer.html#using-normal-transfer", "text": "", "title": "Using Normal Transfer"}, {"location": "offset.html", "text": "Offset The Offset tool is used to duplicate and offset an edge loop at the intersection of boolean operators. This way clean topology in the form of a perimeter loop is established in the periphery of the boolean operation. It is not a general purpose offset tool. Offset can deal with tricky geometry overlaps and significantly reduce manual cleanup work as a result of these overlaps. Note It is wise to run BooleanCleanup before using the Offset tool. Selection Select a loop of edges or vertices. It should be a cyclic selection, otherwise non-manifold geometry will be created. cyclic edge loop at boolean intersection or chamfer, a vert loop is fine as well Using Offset", "title": "Offset"}, {"location": "offset.html#offset", "text": "The Offset tool is used to duplicate and offset an edge loop at the intersection of boolean operators. This way clean topology in the form of a perimeter loop is established in the periphery of the boolean operation. It is not a general purpose offset tool. Offset can deal with tricky geometry overlaps and significantly reduce manual cleanup work as a result of these overlaps. Note It is wise to run BooleanCleanup before using the Offset tool.", "title": "Offset"}, {"location": "offset.html#selection", "text": "Select a loop of edges or vertices. It should be a cyclic selection, otherwise non-manifold geometry will be created. cyclic edge loop at boolean intersection or chamfer, a vert loop is fine as well", "title": "Selection"}, {"location": "offset.html#using-offset", "text": "", "title": "Using Offset"}, {"location": "orphan_stashes.html", "text": "Orphan Stashes Oprhan Stashes are stashes, that are no longer referenced by any scene object. Removing Stashes from an object, or deleting the object itself, will not remove the previously stashed objects. Instead they can still be viewed and retrieved using the View Orphan Stashes tool. The View Orphan Stashes tool then is essentially the same as the ViewStashes tool, although a bit more limited. Selection No selection is required, only the presence of orphan stashes in the blend file. Using View Orphan Stashes", "title": "<PERSON><PERSON><PERSON>"}, {"location": "orphan_stashes.html#orphan-stashes", "text": "Oprhan Stashes are stashes, that are no longer referenced by any scene object. Removing Stashes from an object, or deleting the object itself, will not remove the previously stashed objects. Instead they can still be viewed and retrieved using the View Orphan Stashes tool. The View Orphan Stashes tool then is essentially the same as the ViewStashes tool, although a bit more limited.", "title": "<PERSON><PERSON><PERSON>"}, {"location": "orphan_stashes.html#selection", "text": "No selection is required, only the presence of orphan stashes in the blend file.", "title": "Selection"}, {"location": "orphan_stashes.html#using-view-orphan-stashes", "text": "", "title": "Using View Orphan Stashes"}, {"location": "plug_creation.html", "text": "Plug Creation The Create Plug tool is used to create a complete plug from one or multiple mesh objects. Every plug consists of exactly one plug mesh and one plug handle. Optionally, there can be any number of secondaray subset mesh objects. Selection In object mode, select one or multiple mesh objeects. The active (last selected) object will become the plug mesh, any additional objects will become plug subets. The Create Plug tool will then create the plug handle from the plug mesh, but only in part. You will have to finish the plug handle yourself, taking care to create roughly even topology and to avoid ngons. Using Create Plug Validate Plug The Validate Plug tool is used to check a plug for errors and confirm everything is in order. It will analyze and list all a plug contents and properties. In addtion, it can hide support objects such as Deformers and Occluders and generate new UUIDs. Selection Any object can be selected. For the tool to find all a plug's components, you should select the plug handle however. Add Plug to Library Once a plug is confirmed to be working, it can be added to any Plug library, that isn't locked, using the Add Plug to Library tool. Selection Select the plug handle.", "title": "Plug Creation"}, {"location": "plug_creation.html#plug-creation", "text": "The Create Plug tool is used to create a complete plug from one or multiple mesh objects. Every plug consists of exactly one plug mesh and one plug handle. Optionally, there can be any number of secondaray subset mesh objects.", "title": "Plug Creation"}, {"location": "plug_creation.html#selection", "text": "In object mode, select one or multiple mesh objeects. The active (last selected) object will become the plug mesh, any additional objects will become plug subets. The Create Plug tool will then create the plug handle from the plug mesh, but only in part. You will have to finish the plug handle yourself, taking care to create roughly even topology and to avoid ngons.", "title": "Selection"}, {"location": "plug_creation.html#using-create-plug", "text": "", "title": "Using Create Plug"}, {"location": "plug_creation.html#validate-plug", "text": "The Validate Plug tool is used to check a plug for errors and confirm everything is in order. It will analyze and list all a plug contents and properties. In addtion, it can hide support objects such as Deformers and Occluders and generate new UUIDs.", "title": "Validate Plug"}, {"location": "plug_creation.html#selection_1", "text": "Any object can be selected. For the tool to find all a plug's components, you should select the plug handle however.", "title": "Selection"}, {"location": "plug_creation.html#add-plug-to-library", "text": "Once a plug is confirmed to be working, it can be added to any Plug library, that isn't locked, using the Add Plug to Library tool.", "title": "Add Plug to Library"}, {"location": "plug_creation.html#selection_2", "text": "Select the plug handle.", "title": "Selection"}, {"location": "plug_resources.html", "text": "Plug Libraries MESHmachine supports 3rd party plug libraries ,. which are folders filled with plug blend files and icons. Check out the addon preferences for details on how to add a library. The following plug libraries are not a part of MESHmachine itself and have been created by their respective authors. TAGAPAW AbstractScape Vol. 1 Oleg Multifunction Plugs New Media Supply Plugs for MESHmachine George Yong SOLID Plugs Vol.1 Spencer R Low-Tech Plugs Unis Correct Topology Plugs Get in touch if you want to be listed here.", "title": "Plug Resources"}, {"location": "plug_resources.html#plug-libraries", "text": "MESHmachine supports 3rd party plug libraries ,. which are folders filled with plug blend files and icons. Check out the addon preferences for details on how to add a library. The following plug libraries are not a part of MESHmachine itself and have been created by their respective authors.", "title": "Plug Libraries"}, {"location": "plug_resources.html#tagapaw", "text": "", "title": "TAGAPAW"}, {"location": "plug_resources.html#abstractscape-vol-1", "text": "", "title": "AbstractScape Vol. 1"}, {"location": "plug_resources.html#oleg", "text": "", "title": "<PERSON><PERSON>"}, {"location": "plug_resources.html#multifunction-plugs", "text": "", "title": "Multifunction Plugs"}, {"location": "plug_resources.html#new-media-supply", "text": "", "title": "New Media Supply"}, {"location": "plug_resources.html#plugs-for-meshmachine", "text": "", "title": "Plugs for MESHmachine"}, {"location": "plug_resources.html#george-yong", "text": "", "title": "<PERSON>"}, {"location": "plug_resources.html#solid-plugs-vol1", "text": "", "title": "SOLID Plugs Vol.1"}, {"location": "plug_resources.html#spencer-r", "text": "", "title": "<PERSON>"}, {"location": "plug_resources.html#low-tech-plugs", "text": "", "title": "Low-Tech Plugs"}, {"location": "plug_resources.html#unis", "text": "", "title": "Unis"}, {"location": "plug_resources.html#correct-topology-plugs", "text": "Get in touch if you want to be listed here.", "title": "Correct Topology Plugs"}, {"location": "plugs_introduction.html", "text": "Plugs Introduction The Plug tool is used to quickly add prepared details to a model. Selection In object mode, select a single plug handle, not a plug mesh, and the target model you want to plug into. If a plug is brought into the scene from a plug library, the handle will be selected automatically. Surface snapping and alignment will be set up as well. This way you can use the translate tool g and with it active, hold down CTRL to snap the plug to a surface. All that is left to do is shift select the target object, followed by running the plug tool. plug handle and target sphere, which is active Using Plugs", "title": "Plugs Introduction"}, {"location": "plugs_introduction.html#plugs-introduction", "text": "The Plug tool is used to quickly add prepared details to a model.", "title": "Plugs Introduction"}, {"location": "plugs_introduction.html#selection", "text": "In object mode, select a single plug handle, not a plug mesh, and the target model you want to plug into. If a plug is brought into the scene from a plug library, the handle will be selected automatically. Surface snapping and alignment will be set up as well. This way you can use the translate tool g and with it active, hold down CTRL to snap the plug to a surface. All that is left to do is shift select the target object, followed by running the plug tool. plug handle and target sphere, which is active", "title": "Selection"}, {"location": "plugs_introduction.html#using-plugs", "text": "", "title": "Using Plugs"}, {"location": "preferences.html", "text": "In the addon preferences, you can adjust keymaps and various settings governing MESHmachine behavior and appeareance. The Plugs tab allows you can manage your Plug libraries. Legacy Line Smoothing With the switch in version 0.9 to utilizing the gpu module instead of the bgl module (due to its planed removal), any lines drawn by MESHmachine in the 3D view will be jagged. To avoid this, you can enable Legacy Line Smoothing in the addon preferences. This will continue to work for as long as the bgl module is still supplied with Blender. Experimental Features Use Legacy Line Smoothing in the VIEW3D section As of version 0.7, MESHmachine offers a few experimental features, that can be enabled in the addon preferences. a new approach for transfering normals next to sharp edges a flip option for Boolean Cleanup , useful for mesh cut/knife intersect topology the Offset Cut tool Attention Experimental features are regarded as unfinished , largely untested , completely undocumented and excluded from product support . Use at your own risk.", "title": "Preferences"}, {"location": "preferences.html#_1", "text": "In the addon preferences, you can adjust keymaps and various settings governing MESHmachine behavior and appeareance. The Plugs tab allows you can manage your Plug libraries.", "title": ""}, {"location": "preferences.html#legacy-line-smoothing", "text": "With the switch in version 0.9 to utilizing the gpu module instead of the bgl module (due to its planed removal), any lines drawn by MESHmachine in the 3D view will be jagged. To avoid this, you can enable Legacy Line Smoothing in the addon preferences. This will continue to work for as long as the bgl module is still supplied with Blender.", "title": "Legacy Line Smoothing"}, {"location": "preferences.html#experimental-features", "text": "Use Legacy Line Smoothing in the VIEW3D section As of version 0.7, MESHmachine offers a few experimental features, that can be enabled in the addon preferences. a new approach for transfering normals next to sharp edges a flip option for Boolean Cleanup , useful for mesh cut/knife intersect topology the Offset Cut tool Attention Experimental features are regarded as unfinished , largely untested , completely undocumented and excluded from product support . Use at your own risk.", "title": "Experimental Features"}, {"location": "quad_corner.html", "text": "Quad Corner shortcut yq edit mode The Quad Corner tool is used to convert a triangular bevel corner into a quad corner . Selection Corner polygons with three corner vertices. polgons of a triangular corner Using Quad Corner", "title": "Quad Corner"}, {"location": "quad_corner.html#quad-corner", "text": "shortcut yq edit mode The Quad Corner tool is used to convert a triangular bevel corner into a quad corner .", "title": "Quad Corner"}, {"location": "quad_corner.html#selection", "text": "Corner polygons with three corner vertices. polgons of a triangular corner", "title": "Selection"}, {"location": "quad_corner.html#using-quad-corner", "text": "", "title": "Using Quad Corner"}, {"location": "quick_patch.html", "text": "Quick Patch shortcut yq object mode The Quick Patch tool can be used to create an all-quad curved surface patch, with the primary intention to use it as a stash and normal source . Selection A single mesh object Using Quick Patch", "title": "<PERSON> Patch"}, {"location": "quick_patch.html#quick-patch", "text": "shortcut yq object mode The Quick Patch tool can be used to create an all-quad curved surface patch, with the primary intention to use it as a stash and normal source .", "title": "<PERSON> Patch"}, {"location": "quick_patch.html#selection", "text": "A single mesh object", "title": "Selection"}, {"location": "quick_patch.html#using-quick-patch", "text": "", "title": "Using Quick Patch"}, {"location": "real_mirror.html", "text": "Real Mirror shortcut yr object mode The Real Mirror tool is used to turn the results of the mirror modifer into real geometry. This is done for two main reasons: To get a proper origin for the mirrored part, so object space texturing works as expected. To mirror custom normals, which the mirror modifier can't do 1 . Selection One or multiple objects, with one or multiple mirror modifiers, that are visible and renderable and have at least one mirror axis checked. Using Real Mirror Blender's mirror modifier has received support for custom normals some time after 2.80 ↩", "title": "Real Mirror"}, {"location": "real_mirror.html#real-mirror", "text": "shortcut yr object mode The Real Mirror tool is used to turn the results of the mirror modifer into real geometry. This is done for two main reasons: To get a proper origin for the mirrored part, so object space texturing works as expected. To mirror custom normals, which the mirror modifier can't do 1 .", "title": "Real Mirror"}, {"location": "real_mirror.html#selection", "text": "One or multiple objects, with one or multiple mirror modifiers, that are visible and renderable and have at least one mirror axis checked.", "title": "Selection"}, {"location": "real_mirror.html#using-real-mirror", "text": "Blender's mirror modifier has received support for custom normals some time after 2.80 ↩", "title": "Using Real Mirror"}, {"location": "refuse.html", "text": "Refuse shortcut yr edit mode Refuse is a quick way, to Unfuse and <PERSON><PERSON> right after. For all practical purposes, this enables you to edit existing Fuse/Bevel/Bridge surfaces. Keep in mind While the initial segments for the refuse are determined automatically, the tension is currently not and will be set to its default value of 0.7 or the value you've chosen the last time you ran the Refuse tool in your current session. Selection Selection requirements are the same as for the Unfuse tool: Poly strip - a row of polygons - across the Fuse/Bevel/Bridge surface. The best way to select this is by using the Pick Shortest Path tool via Ctrl + Select Mouse . If there are Ngons on both sides, you can be even faster by loop selecting - via Alt + Select Mouse - while pointing at a rail edge of the Fuse/Bevel/Bridge surface. poly strip across fuse/bevel/bridge surface Note Refuse will automatically expand the selection to the entire Fuse/Bevel/Bridge surface. If it runs into triangles or ngons, it will abort. See Quad Corner for how to deal with triangular Bevel corners. Keep in mind You currently can not select the entire bevel surface on your own to refuse, it has to be a poly strip going across. Using Refuse", "title": "Refuse"}, {"location": "refuse.html#refuse", "text": "shortcut yr edit mode Refuse is a quick way, to Unfuse and <PERSON><PERSON> right after. For all practical purposes, this enables you to edit existing Fuse/Bevel/Bridge surfaces. Keep in mind While the initial segments for the refuse are determined automatically, the tension is currently not and will be set to its default value of 0.7 or the value you've chosen the last time you ran the Refuse tool in your current session.", "title": "Refuse"}, {"location": "refuse.html#selection", "text": "Selection requirements are the same as for the Unfuse tool: Poly strip - a row of polygons - across the Fuse/Bevel/Bridge surface. The best way to select this is by using the Pick Shortest Path tool via Ctrl + Select Mouse . If there are Ngons on both sides, you can be even faster by loop selecting - via Alt + Select Mouse - while pointing at a rail edge of the Fuse/Bevel/Bridge surface. poly strip across fuse/bevel/bridge surface Note Refuse will automatically expand the selection to the entire Fuse/Bevel/Bridge surface. If it runs into triangles or ngons, it will abort. See Quad Corner for how to deal with triangular Bevel corners. Keep in mind You currently can not select the entire bevel surface on your own to refuse, it has to be a poly strip going across.", "title": "Selection"}, {"location": "refuse.html#using-refuse", "text": "", "title": "Using Refuse"}, {"location": "remove_plugs.html", "text": "Remove Plugs The Remove Plugs option, is used to remove individual plugs from a library and hard drive, directly from inside Blender. Keep in mind This completely deletes a plug and cannot be undone. Using Remove Plugs Enable the Remove Plugs checkbox in the Plug Libraries sub menu. Then procceed to select a plug, as if you were to bring it into the scene. A confirmation dialog will appear. You cann abort this via ESC or by clicking outside of it. If you confirm by pressing OK, the plug will be deleted. If you are done, uncheck the Remove Plug checkbox again. Note If you want to remove a larger number of plugs , a better way may be to do it directly in the file browser. Navigate to the plug library and delete the matching plug blends and icons accordingly.", "title": "Remove Plugs"}, {"location": "remove_plugs.html#remove-plugs", "text": "The Remove Plugs option, is used to remove individual plugs from a library and hard drive, directly from inside Blender. Keep in mind This completely deletes a plug and cannot be undone.", "title": "Remove Plugs"}, {"location": "remove_plugs.html#using-remove-plugs", "text": "Enable the Remove Plugs checkbox in the Plug Libraries sub menu. Then procceed to select a plug, as if you were to bring it into the scene. A confirmation dialog will appear. You cann abort this via ESC or by clicking outside of it. If you confirm by pressing OK, the plug will be deleted. If you are done, uncheck the Remove Plug checkbox again. Note If you want to remove a larger number of plugs , a better way may be to do it directly in the file browser. Navigate to the plug library and delete the matching plug blends and icons accordingly.", "title": "Using Remove Plugs"}, {"location": "select.html", "text": "Select shortcut ALT + LMB edit mode The Select tool is is a wrapper around LSelect , SSelect , VSelect and Blender's native loop select tool. It allows you to access all four via a single keymap, which is intedend to be the same as <PERSON><PERSON><PERSON>'s own loop select keymap, which in turn may varry depending on your key config choice. Always Loop Select By default - and in Edge mode - connected sharps edges will be selected, if the entire edge selection consists of sharp edges, otherwise an angle based loop selection is done. However, this behavior can be overriden, with the Always Loop Select option. enforce loop selection, even with a selection of only sharp edges This can be handy, if you are working on a mesh with a lot of sharp edges, and you still want to use Loop over Sharp selection, and avoid having to constantly toggle back into Loop selection mode. Selection Selection requirements are the same as for LSelect, SSelect and Vselect. If they aren't met, <PERSON><PERSON><PERSON>'s native loop select will be called. Using Select", "title": "Select"}, {"location": "select.html#select", "text": "shortcut ALT + LMB edit mode The Select tool is is a wrapper around LSelect , SSelect , VSelect and Blender's native loop select tool. It allows you to access all four via a single keymap, which is intedend to be the same as <PERSON>lender's own loop select keymap, which in turn may varry depending on your key config choice.", "title": "Select"}, {"location": "select.html#always-loop-select", "text": "By default - and in Edge mode - connected sharps edges will be selected, if the entire edge selection consists of sharp edges, otherwise an angle based loop selection is done. However, this behavior can be overriden, with the Always Loop Select option. enforce loop selection, even with a selection of only sharp edges This can be handy, if you are working on a mesh with a lot of sharp edges, and you still want to use Loop over Sharp selection, and avoid having to constantly toggle back into Loop selection mode.", "title": "Always Loop Select"}, {"location": "select.html#selection", "text": "Selection requirements are the same as for LSelect, SSelect and Vselect. If they aren't met, Blender's native loop select will be called.", "title": "Selection"}, {"location": "select.html#using-select", "text": "", "title": "Using Select"}, {"location": "sselect.html", "text": "SSelect The SSelect tool, found in the Select sub-menu, is used to select all connected sharp edges. Sharp Selecting can be easier then Loop Selecting if the edge loop is marked sharp already. Also see the Select tool, for a more convenient way to access SSelect . Selection One or multiple sharp edges. Using SSelect", "title": "SSelect"}, {"location": "sselect.html#sselect", "text": "The SSelect tool, found in the Select sub-menu, is used to select all connected sharp edges. Sharp Selecting can be easier then Loop Selecting if the edge loop is marked sharp already. Also see the Select tool, for a more convenient way to access SSelect .", "title": "SSelect"}, {"location": "sselect.html#selection", "text": "One or multiple sharp edges.", "title": "Selection"}, {"location": "sselect.html#using-sselect", "text": "", "title": "Using SSelect"}, {"location": "stashes_panel.html", "text": "Stashes panel As of MESHmachine 0.7, an active object's stashes can be accesed not only via the ViewStashes tool, but also from the MESHmachine panel in the 3D View's sidebar. Note This panel is the only way to define custom stash names.", "title": "Stashes Panel"}, {"location": "stashes_panel.html#stashes-panel", "text": "As of MESHmachine 0.7, an active object's stashes can be accesed not only via the ViewStashes tool, but also from the MESHmachine panel in the 3D View's sidebar. Note This panel is the only way to define custom stash names.", "title": "Stashes panel"}, {"location": "sweep_stashes.html", "text": "Sweep Stashes Sweeping Stashes will simply unlink stash objects, that where linked to your current scene collection, when the stash parent was appened. The stash objects beeing linked is an unfortunate, and undesired side effect of how Blender appends objects. Selection No selection is required, only the presence of linked stash objects. Using Sweep Stashes", "title": "Sweep <PERSON>"}, {"location": "sweep_stashes.html#sweep-stashes", "text": "Sweeping Stashes will simply unlink stash objects, that where linked to your current scene collection, when the stash parent was appened. The stash objects beeing linked is an unfortunate, and undesired side effect of how <PERSON><PERSON><PERSON> appends objects.", "title": "Sweep <PERSON>"}, {"location": "sweep_stashes.html#selection", "text": "No selection is required, only the presence of linked stash objects.", "title": "Selection"}, {"location": "sweep_stashes.html#using-sweep-stashes", "text": "", "title": "Using Sweep Stashes"}, {"location": "symmetrize.html", "text": "Symmetrize shortcut Alt + x edit mode The Symmetrize tool extends blender's native Symmetrize with the ability to mirror custom normals. Since MESHmachine 0.7, it comes with a flick mode, that allows you to mirror in all 6 object space directions, from a single keymap. Note Mirroring custom normals can be very tricky on the center edge loop. MESHmachine's Symmetrize provides options to deal with this, but they are imperfect and often custom normals will need to be fixed manually in the center region. Modes Version 0.10 introduced a Remove mode, that allows you to remove half the mesh in the chosen direction. Once the tool is active, you can toggle between Symmetrize and Remove using the X key. the HUD changes accordingly to represent Remove mode The same version also introduced the ability to symmetrize (or remove) only the selected parts of a mesh. This behavior is toggled using the S key when the tool is active. partially Symmetrizing only the selected parts Remove Redundant Center If you are symmetrizing the entire mesh (so not only a partial selection), and you aren't symmetizing custom normals, then you will have the option to remove a redundant center edge loop. the threshold slider offers some control to determine what should be considered a redundant center edge Selection Normally, no selection is required, and the entire mesh will be symmetrized. If you want to symmetrize only a specific part, select it and toggle on Selected mode using the S key. Using Symmetrize", "title": "Symmetrize"}, {"location": "symmetrize.html#symmetrize", "text": "shortcut Alt + x edit mode The Symmetrize tool extends blender's native Symmetrize with the ability to mirror custom normals. Since MESHmachine 0.7, it comes with a flick mode, that allows you to mirror in all 6 object space directions, from a single keymap. Note Mirroring custom normals can be very tricky on the center edge loop. MESHmachine's Symmetrize provides options to deal with this, but they are imperfect and often custom normals will need to be fixed manually in the center region.", "title": "Symmetrize"}, {"location": "symmetrize.html#modes", "text": "Version 0.10 introduced a Remove mode, that allows you to remove half the mesh in the chosen direction. Once the tool is active, you can toggle between Symmetrize and Remove using the X key. the HUD changes accordingly to represent Remove mode The same version also introduced the ability to symmetrize (or remove) only the selected parts of a mesh. This behavior is toggled using the S key when the tool is active. partially Symmetrizing only the selected parts", "title": "Modes"}, {"location": "symmetrize.html#remove-redundant-center", "text": "If you are symmetrizing the entire mesh (so not only a partial selection), and you aren't symmetizing custom normals, then you will have the option to remove a redundant center edge loop. the threshold slider offers some control to determine what should be considered a redundant center edge", "title": "Remove Redundant Center"}, {"location": "symmetrize.html#selection", "text": "Normally, no selection is required, and the entire mesh will be symmetrized. If you want to symmetrize only a specific part, select it and toggle on Selected mode using the S key.", "title": "Selection"}, {"location": "symmetrize.html#using-symmetrize", "text": "", "title": "Using Symmetrize"}, {"location": "transfer_stashes.html", "text": "Transfer Stashes shortcut yt , object mode The Transfer Stashes tool is used to copy stashes from one object to another one. Existing stashes on the target object will not be overwritten. The transfered stashes will simply be added to the existing collection. Selection Excatly two objects, where the inactive object carries stashes. the sphere one the left is active and will receive stashes from the sphere on the right, which was selected first Using View Stashes", "title": "Transfer Stashes"}, {"location": "transfer_stashes.html#transfer-stashes", "text": "shortcut yt , object mode The Transfer Stashes tool is used to copy stashes from one object to another one. Existing stashes on the target object will not be overwritten. The transfered stashes will simply be added to the existing collection.", "title": "Transfer Stashes"}, {"location": "transfer_stashes.html#selection", "text": "Excatly two objects, where the inactive object carries stashes. the sphere one the left is active and will receive stashes from the sphere on the right, which was selected first", "title": "Selection"}, {"location": "transfer_stashes.html#using-view-stashes", "text": "", "title": "Using View Stashes"}, {"location": "turn_corner.html", "text": "Turn Corner shortcut yt edit mode The Turn Corner tool is used to re-direct the flow of a chamfer. Selection Corner quad where 3 chamfers meet. Using Turn Corner", "title": "Turn Corner"}, {"location": "turn_corner.html#turn-corner", "text": "shortcut yt edit mode The Turn Corner tool is used to re-direct the flow of a chamfer.", "title": "Turn Corner"}, {"location": "turn_corner.html#selection", "text": "Corner quad where 3 chamfers meet.", "title": "Selection"}, {"location": "turn_corner.html#using-turn-corner", "text": "", "title": "Using Turn Corner"}, {"location": "unbevel.html", "text": "Unbevel shortcut yb edit mode Unbevel is a quick way, to <PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON> right after. It turns a Fuse/Bevel/Bridge surface into a hard edge. Selection Selectiton requirements are the same as for the Unfuse tool: Poly strip - a row of polygons - across the Fuse/Bevel/Bridge surface. The best way to select this is by using the Pick Shortest Path tool via Ctrl + Select Mouse . If there are Ngons on both sides, you can be even faster by loop selecting via Alt + Select Mouse , while pointing at a rail edge of the Fuse/Bevel/Bridge surface. poly strip across fuse/bevel/bridge surface Note Unbevel will automatically expand the selection to the entire Fuse/Bevel/Bridge surface. If it runs into triangles or ngons, it will abort. See Quad Corner for how to deal with triangular Bevel corners. Keep in mind You currently can not select the entire bevel surface on your own to unbevel, it has to be a poly strip going across. Using Unbevel", "title": "<PERSON><PERSON><PERSON>"}, {"location": "unbevel.html#unbevel", "text": "shortcut yb edit mode <PERSON><PERSON>vel is a quick way, to <PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON> right after. It turns a Fuse/Bevel/Bridge surface into a hard edge.", "title": "<PERSON><PERSON><PERSON>"}, {"location": "unbevel.html#selection", "text": "Selectiton requirements are the same as for the Unfuse tool: Poly strip - a row of polygons - across the Fuse/Bevel/Bridge surface. The best way to select this is by using the Pick Shortest Path tool via Ctrl + Select Mouse . If there are Ngons on both sides, you can be even faster by loop selecting via Alt + Select Mouse , while pointing at a rail edge of the Fuse/Bevel/Bridge surface. poly strip across fuse/bevel/bridge surface Note Unbevel will automatically expand the selection to the entire Fuse/Bevel/Bridge surface. If it runs into triangles or ngons, it will abort. See Quad Corner for how to deal with triangular Bevel corners. Keep in mind You currently can not select the entire bevel surface on your own to unbevel, it has to be a poly strip going across.", "title": "Selection"}, {"location": "unbevel.html#using-unbevel", "text": "", "title": "Using Unbevel"}, {"location": "unchamfer.html", "text": "Unchamfer shortcut yc edit mode The Unchamfer tool turns a chamfer into a hard edge. Selection Poly strip - a row of polygons - along the chamfer. The best way to select a chamfer is usually by loop selecting it - by default via Alt + Select Mouse , while pointing at an sweep edge of the chamfer. The sequence of the chamfer polygons determine the direction of the hard edge. If Unchamfer is run on a single polygon, the direction will be determined by edge length, but can can also be reversed in the redo/tool properties panel. Using Unchamfer", "title": "<PERSON><PERSON><PERSON><PERSON>"}, {"location": "unchamfer.html#unchamfer", "text": "shortcut yc edit mode The Unchamfer tool turns a chamfer into a hard edge.", "title": "<PERSON><PERSON><PERSON><PERSON>"}, {"location": "unchamfer.html#selection", "text": "Poly strip - a row of polygons - along the chamfer. The best way to select a chamfer is usually by loop selecting it - by default via Alt + Select Mouse , while pointing at an sweep edge of the chamfer. The sequence of the chamfer polygons determine the direction of the hard edge. If Unchamfer is run on a single polygon, the direction will be determined by edge length, but can can also be reversed in the redo/tool properties panel.", "title": "Selection"}, {"location": "unchamfer.html#using-unchamfer", "text": "", "title": "Using <PERSON><PERSON><PERSON><PERSON>"}, {"location": "unfuck.html", "text": "Unf*ck shortcut yx edit mode Unf*ck aligns vertices along an implicit spline curve. This is especially useful in situations were the Bevel tool overshoots vertex positions. bevel overshoot Selection Edge loop of at least 4 edges. The best way to select them is by using the Pick Shortest Path tool via Ctrl + Select Mouse . f*cked up geometry Note Note, that the 2 vertices at the end of the selected edges will not be changed, they are only used internally to setup the implicit spline curve. Using Unf*ck", "title": "Unf*ck"}, {"location": "unfuck.html#unfck", "text": "shortcut yx edit mode Unf*ck aligns vertices along an implicit spline curve. This is especially useful in situations were the Bevel tool overshoots vertex positions. bevel overshoot", "title": "Unf*ck"}, {"location": "unfuck.html#selection", "text": "Edge loop of at least 4 edges. The best way to select them is by using the Pick Shortest Path tool via Ctrl + Select Mouse . f*cked up geometry Note Note, that the 2 vertices at the end of the selected edges will not be changed, they are only used internally to setup the implicit spline curve.", "title": "Selection"}, {"location": "unfuck.html#using-unfck", "text": "", "title": "Using Unf*ck"}, {"location": "unfuse.html", "text": "Unfuse shortcut yd edit mode Unfuse turns a curved surface created by <PERSON><PERSON> or the Bevel and Bridge tools into a chamfer. Selection Poly strip - a row of polygons - across the Fuse/Bevel/Bridge surface. The best way to select this is by using the Pick Shortest Path tool via Ctrl + Select Mouse . If there are Ngons on both sides, you can be even faster by loop selecting - by default via Alt + Select Mouse - while pointing at a rail edge of the Fuse/Bevel/Bridge surface. poly strip across fuse/bevel/bridge surface Note Unfuse will automatically expand the selection to the entire Fuse/Bevel/Bridge surface. If it runs into triangles or ngons, it will abort. See Quad Corner for how to deal with triangular Bevel corners. Keep in mind You currently can not select the entire bevel surface on your own to unfuse, it has to be a poly strip going across. Using Unfuse", "title": "Unfuse"}, {"location": "unfuse.html#unfuse", "text": "shortcut yd edit mode Unfuse turns a curved surface created by <PERSON><PERSON> or the Bevel and Bridge tools into a chamfer.", "title": "Unfuse"}, {"location": "unfuse.html#selection", "text": "Poly strip - a row of polygons - across the Fuse/Bevel/Bridge surface. The best way to select this is by using the Pick Shortest Path tool via Ctrl + Select Mouse . If there are Ngons on both sides, you can be even faster by loop selecting - by default via Alt + Select Mouse - while pointing at a rail edge of the Fuse/Bevel/Bridge surface. poly strip across fuse/bevel/bridge surface Note Unfuse will automatically expand the selection to the entire Fuse/Bevel/Bridge surface. If it runs into triangles or ngons, it will abort. See Quad Corner for how to deal with triangular Bevel corners. Keep in mind You currently can not select the entire bevel surface on your own to unfuse, it has to be a poly strip going across.", "title": "Selection"}, {"location": "unfuse.html#using-unfuse", "text": "", "title": "Using Unfuse"}, {"location": "view_stashes.html", "text": "View Stashes shortcut yv edit and object mode The View Stashes tool is used to view, edit, swap, retrieve and clear the stashes associated with an object. As of version 0.8, it can also be used to set the cursor to the location and orientation of a stash object. Also check out the Stashes Panel , much provides an alternative way to interact with stashes. Selection An object carrying stashes, in object or edit mode. this sphere has 2 stashes, as indicated by the stash counter at the top of the 3d view Using View Stashes", "title": "View Stashes"}, {"location": "view_stashes.html#view-stashes", "text": "shortcut yv edit and object mode The View Stashes tool is used to view, edit, swap, retrieve and clear the stashes associated with an object. As of version 0.8, it can also be used to set the cursor to the location and orientation of a stash object. Also check out the Stashes Panel , much provides an alternative way to interact with stashes.", "title": "View Stashes"}, {"location": "view_stashes.html#selection", "text": "An object carrying stashes, in object or edit mode. this sphere has 2 stashes, as indicated by the stash counter at the top of the 3d view", "title": "Selection"}, {"location": "view_stashes.html#using-view-stashes", "text": "", "title": "Using View Stashes"}, {"location": "vselect.html", "text": "Vselect The VSelect tool, found in the Select sub-menu, can be used to quickly select geometry based on vertex group membership. Also see the Select tool, for a more convenient way to access VSelect . Selection If nothing is selected, all vertex groups of the mesh will be cyclced through. Otherwise only the vertex groups the selected verts, edges or faces belong to will be availble to choose from.. Using VSelect", "title": "VSelect"}, {"location": "vselect.html#vselect", "text": "The VSelect tool, found in the Select sub-menu, can be used to quickly select geometry based on vertex group membership. Also see the Select tool, for a more convenient way to access VSelect .", "title": "Vselect"}, {"location": "vselect.html#selection", "text": "If nothing is selected, all vertex groups of the mesh will be cyclced through. Otherwise only the vertex groups the selected verts, edges or faces belong to will be availble to choose from..", "title": "Selection"}, {"location": "vselect.html#using-vselect", "text": "", "title": "Using VSelect"}, {"location": "wedge.html", "text": "Wedge shortcut yg edit mode The Wedge tool is used to develop a primitive mesh corner into a more complex situation, which I refer to as a Wedge . Selection a single edge, with at least one end splitting off into 2 other edges this edge splits off into two edges on both ends, so the wedge can be flipped Using Wedge", "title": "Wedge"}, {"location": "wedge.html#wedge", "text": "shortcut yg edit mode The Wedge tool is used to develop a primitive mesh corner into a more complex situation, which I refer to as a Wedge .", "title": "Wedge"}, {"location": "wedge.html#selection", "text": "a single edge, with at least one end splitting off into 2 other edges this edge splits off into two edges on both ends, so the wedge can be flipped", "title": "Selection"}, {"location": "wedge.html#using-wedge", "text": "", "title": "Using Wedge"}, {"location": "whatsnew.html", "text": "0.17 2024-11-22 Small update. Blender 4.3 does not seem to require any changes, but I've ticked a few things of my todo list and fixed two small issues with this release. LSelect (and the Select wrapper) now allow you to limit the loop selection via a step count. limit loop selections using a step count Unf*ck now supports multiple separate selections at once. multiple indepented edge loop selections adjusted in one go Fuse , Refuse and QuadCorner will now maintain the material index of the selection. material indices are now maintained for meshes with multiple materials Finally, I've added a modal wrapper for LoopTool's Space tool. LoopTools' Space now has a modal wrapper As always, check out the changelog for a list of all changes. 0.16 2024-07-10 This release bring some minor tweaks to stashing behavior . Previously modifiers on stash objects were only applied when invoking the tool with with ALT mod key being held. Now, it's mode dependent. In Object mode modifiers will be applied, unless ALT is being held. In Edit Mesh mode modifiers are not applied, unless ALT is being held. The tooltip will reflect that accordingly, if you ever are in doubt, and the fading wire will give you a clue as well of course. The BooleanApply tool now properly supports redoing, and again you now have have control over how modifiers should be treated on the stash objects, this time from the redo panel. By default they will be applied for the stashes created from the boolean operand objects, whereas the boolean host object's stash will not have the mods applied. I think these are more sensible defaults, when working with objects that have modifiers - which was never really a focus for me when developing MESHmachine and the workflows around it. There are also a few fixes and you can see them all in the changelog . Blender 4.2 seems to work without issues, and so now everything from Blender 3.6-4.2 is supported. If you find anything causing problems, please let me know . 0.15.4 2024-05-16 Bad day. 0.15.3 2024-05-16 This is yet another mostly bugfix release. In the previous build I accidentallly introduced an exception when switching the Fuse tool to BRIDGE mode. The Symmetrize tool gets the option to not mirror vertex groups now, which is the new default behavior. If you dislike this, please let me know, and I can find a better solution. I chose to do this primarily due to vertex group based Bevels I use in HyperCursor. There are further improvments to the AutoSmooth mod setup in Blender 4.1, by the Boolean tool, when using the S smooth shade toggle. Also, creating stashes is now prevented for non-mesh objects, which always should have been like this. See the changelog for a list of all changes. 0.15.2 2024-04-10 This is a bugfix release for the most part, fixing a few issues that have come up. The newly introduced always_loop_select ability of the Select tool/wrapper, unintentionally prevented doing additional edge selections, after it had made one multi-edge selection already. The Boolean tool - when adding an Auto Smooth mod - now does it using a faster more direct method. NormalTransfer and Offset Cut , both fix rare exceptions, and when bringing a Plug into the scene, MESHmachine will now automatically remove any Auto Smooth mod, that may be present on it. Blender adds these mods automatically on all mesh objects, that were created in Blender 4 or earlier, if they had the auto smooth prop enabled. Finally, there has been some HyperCursor unreleased integration work, that ensures Geometry Gizmos are maintained when using MESHmachine's fillet tool set - so Fuse, Refuse, Unfuse, Unchamfer and Unbevel. For Fuse and Refuse, even vertex groups are maintained across each sweep's verts. Finally, the integrated updated has been updated to always expose the Re-Scan button. 0.15.1 2024-03-19 This was a tiny silent release, fixing a small oversight. 0.15 2024-03-18 This release brings Blender 4.1 support, which introduced changes to (Auto)Smooth behavior . Beyond that, there are a couple of improvements on the Boolean , Symmetrize and Select tools. With the Boolean tool in Blender 4.1, when Auto Smooth is enabled through the modifier, either on the operand object(s), or on the active object itself, because you are toggling it in the modal using the S key, the mod will be added towards the end of the stack, but always before Mirror or Array mods, they any of these be at the end of the stack. Also operand objects are now also hidden using cycles visibility settings, not only using the hide_render prop. Symmetrize now exposes the angle threshold used for redundant center edge removal The Select wrapper has received an Always Loop Select toggle The Stashes HUD has been improved as well, and will no longer collide with MACHIN3tool's Focus HUD. Get Support The GetSupport tool has been made more accessible and is placed at the top of the addon preferences It will now - in Blender 4.1 - automatically open the readme.html with instructions in your web browser. Those same instructions can be found here in the docs of course. Integrated Updater Finally, there is now an integrated way to do an update installation still from a .zip file! from inside of Blender. So no longer, will you have to do it from the file browser, to maintain your previous settings. It also - by default - ensures your plug assets are not lost, should you have kept them in the addon location still. Also placed at the top of the addon preferences: the new integrated updater If you have just downloaded your future MESHmachine update, the updater will find it for you! Keep in mind Since this feature is only introduced now in 0.15, it will only be of use for upcoming releases, so can't be used to install this very 0.15 update yet. In the screenshot above, old versions are used for demonstration. But of course you could downgrade with this tool as well. more As always, check the changelog for a list of all changes. Pricing I've decided to bump the price a little, to counter a general downtrend in revenue, in face of rising living and so development costs, that I have observed for over a year now. The reasons for this are not clear, but it's likely a mix of multiple: post-COVID era, reaching market saturation (?), increased competition, lack of new products and social media activity, while my main focus remains on getting HyperCursor out, gen AI, inflation, layoffs, etc. For now, I still want to avoid charging for updates, but it may be inevitable. I'll see how it goes. If you are still reading, note that I have a Patreon account, and HyperCursor is in pre-release. Thanks for your support! 0.14 2023-11-14 This is the Blender 4.0 compatibility release. Check out the changelog for a list of all changes. 0.13 2023-06-28 Blender 3.6 - a new LTS release is out, and it finally allows for the complete removal of the legacy bgl module. Beyond the bgl removal, there are a few smaller tweaks and improvements, such as new mapping methods for Normal Transfers, improved Symmetrize redundant center edge removal, and finally extremly reliable timer modals, which no longer suffer from occasional, unintended speed ups, which could be quite annoying for the Add Boolean tool for instance. Upon popular request, I've also added the Tutorial.blend file again, which was originally supplied with the pre-0.6 releases. You can load it from the MESHmachine help panel in the 3D view. Furthermore, the MESHmachine sidebar panel can now be completely disabled in the addon prefs. See the changelog for a list of all changes 0.12 2022-12-30 A small feature release, it focuses mostly on playing nice alongside HyperCursor , which is in pre-release on Patreon since Christmas Eve. Consequentially the boolean tools have been updated. BooleanDuplicate has been rewritten completely and will now duplicate the entire object tree, which includes modifier objects, even those that aren't parented. It will now also no longer initiate the Translate tool at the end, which allows for the operator props to remain accessible from the redo panel. Split Booleans from the AddBoolean tool have been made more robust. The Symmetrize tool will now by default remove the center edge loop, it it's redundant, based on the edges' angles. Fuse , Change Width , etc have been fine-tuned to better work at varying object scales. See the changelog for a complete list of changes. Happy New Year! 0.11.2 2022-04-30 This release hopefully resolves some odd 3.1 related crashes. I've also added small, one-time thank you message, when opening the addon preferences forfthe first time. 0.11.1 2022-04-24 The previous release has been very solid. So besides a few minor tweaks and adjusments 0.11.1 only really prevents cutters created by the Boolean tool from being rendered. 0.11 2022-03-09 This is the Blender 3.1 release. In addition to 3.1 support, 0.11 also provides a permanent solution to the vertex group memory issue affecting NormalTransefr , Conform and Plug , that was introduced in Blender 3.0, and for which 0.10 only provided a workaround, and one that didn't include Plug. And that's about it. Lately, I was rather occupied with PUNCHit and MACHIN3tools and even some design work for once. Oh, I also have a Patreon account now, where I will do pre-releases of upcoming addons, and where I share blend files , if I manage to find time for art. So if you are interested in further supporting my work, that would be a good way to do it. It is very much appreciated! Thank you for considering! 0.10 2021-12-24 This is a small feature update. Both new features are based on user feedback, so thanks to you guys for suggesting these. Boolean The Duplicate Boolean tool is now fully recursive, which means, it can duplicate or instance complex boolean setups, where the boolean operands themselves have boolean modifiers. This includes BoxCutter insets. Note You still only need to have the main object selected, and all cutters can remain hidden. Symmetrize The Symmetrize tool now has a Remove mode, which you can toggle using the X key. Instead of mirroring the mesh along one axis and in one direction, it will instead remove half the mesh in the chosen direction. Furthermore, using the S key you can now also choose to affect only the selected parts of a mesh. This works for both - Symmetrize and Remove mode. More In addition there are a few fixes in regards to MACHIN3tools' group empties. Check out the changelog for details. Merry Christmas, enjoy the holidays! 0.9.1 2021-12-18 This mostly a bugfix release, but it also improves Flatten and Unchamfer. Blender 3.0 unfortunatly broke the NormalTransferand Conform tools, due a bug relating to vertex groups . Furthermore, due to a change in keymap property representation in Blender 3.0, it was no longer possible to add the Split or Delete tools (depending on your keymap choice of Y or X ) to the MESHmachine menu. This has been fixed now. Check out the changelog for details. 0.9 2021-11-25 This is the Blender 3.0 release. Note, that at the time of this release, Blender 3.0 is still in beta. Alongside 3.0, Blender 2.93 LTS is still being supported, but support for any earlier versions is dropped now. This release also no longer relies on Blender's bgl module for VIEW3D drawing. However, if you want MESHmachine to draw smooth, anti-aliased lines, you need to enable Use Legacy Line Smoothing in the addon preferences , which still uses bgl . Boolean This release adds the Duplicate Boolean tool, which is used to easily duplicate or instance objects with one or multiple boolean modifiers, including all the boolean objects - aka \"cutters\" - even if they are hidden. The Apply Boolean tool has been updated to support applying boolean mods on multi-object selections. Unfuse and Unbevel Both tools will no longer set sharp edges by default, unless the face selection actually consists of smooth faces. More There are various smaller tweaks and adjustments, such as improvements to the GetSupport tool and the removal of legacy code for pre-2.93 case handling. Finally note that, due to the number of tools in the addon, not everything could be fully tested in 3.0, so some issues may still arise and should be reported . Plugs To conclude, here is a very interesting, topology-focused use case for Plugs , which you may want to check out, by Unis on gumroad. 0.8.2 A tiny bugfix release addressing an issue on some linux systems. Also, disable Wedge debug output, which was accidentally left enabled in 0.8.1. 0.8.1 This is a hotfix release for the Wedge tool. No other changes. 0.8 MESHmachine 0.8 is the official 2.93 release, and I'd ask you to update to 2.93, if you haven't already. 2.93 is the latest stable release, and the latest LTS release at the same time. Any issues occurring only in earlier versions will not be addressed going forward. This is a feature release adding the Wedge tool , Split Booleans and related tooling, and the ability to set the Cursor to Stashes . Beyond that there have been some tweaks and a few fixes. Cursor to Stash Split Booleans Wedge Check out the changelog for the full list of changes. 0.7.2 The second release in the 0.7 cycle resolves an issue with subset plug creation. It also adds the ability to stash evaluated meshes by holding ALT , and improves dealing with child objects when swapping stashes. See the changelog for details. 0.7.1 This is a small bugfix release with some UI tweaks. If you have 0.7 installed already, I'd consider this an optional update, depending on whether you require any of the supplied fixes or tweaks . The easiest way to install a bugfix release like this one, is to simply overwrite the existing MESHmachine addon folder with the MESHmachine folder in the 0.7.1 zip file. There's no need to delete anything. 0.7 The 0.7 release is the first real feature release since 0.6, which released 2.5 years ago. While there have been the 2.80 port and a few compatibility releases - some with new features sneaked in - there hasn't been a lot of development happening since 0.6. This is due to the complete rewrite of DECALmachine for Blender 2.80, as well as the following updates creating a full decal export pipeline , which took 2+ years in total. MESHmachine 0.7 then is the result of finally spending some new development time on MESHmachine again, and pulling together a few unreleased features I had been sitting on for those past 2+ years. It brings various improvements in regards to existing workflows, some new tools and features, and new documentation. 0.7 is only the start though, I will make MESHmachine a main focus of my development efforts for 2021, and likely 2022 as well. Installation & Support Please make sure to check the updated installation instructions , as well as the preferences . Also see this , if you require product support. Boolean MESHmachine 0.7 adds two boolean tools , for adding and applying booleans. Unique about them - besides the convenience - is the integration with stashes . Selection While VSelect was present in 0.6 already, LSelect and SSelect only came with 0.6.10 and remained undocumented - until now. Still, buried in a sub-menu, all three could be awkard to use, considering how frequent selections are done while modeling. The new Select tool rectifies this. Symmetrize MESHmachine's Symmetrize has been a bit restrictive in the past, as it required 3 separate keymaps to mirror in 3 directions, and at all times an awareness of how your object is aligned. If you wanted to mirror in one of the other 3 directions, you'd have to use the redo panel. Version 0.7 adds the flick mode , which means you can use a single keymap to mirror in all 6 directions, in a manner very similar to how pie menus work. Stashes Stashes, a central feature of MESHmachine, are becomming even better. You can now conveniently access them from the sidebar, complementing the View Stashes tool. From here you can now even define custom names. In addition to editing and retrieving stashes, you can now also swap them with the active object. Furthermore, removing object stashes, and dealing with orphan stashes has been simplified. You can now create stashes from face selections, and you can sweep stashes if they end up cluttering your scene after appending objects. More If you ever needed to delete a plug, know that you can do that easily now . And if you ever forgot to create a stash and needed a normal source to flx a shading issue, perhaps after plugging, you can try creating a Quick Patch with MESHmachine 0.7. Experimental Features I have decided to release a few experimental features with this release, the most exciting being the OffsetCut tool. Please understand, that experimental features are undocumented, untested and not covered by product support. I consider them unfinished, see this for details. In conclusion, I want to thank you for your patience and for your support. You can check out the changelog for details on this release, as well as view all new videos in this playlist . 0.6.13 This is a tiny bugfix release for users of 2.90, that fixes an exception when adding a plug to a library. 0.6.12 This release ensures compatibility with Blender 2.90 and adds ALT navigation support for users of the Industry Compatibly keymap. It also changes how panels are registered to support proper workspace filtering. 0.6.11 This release ensures compatibility with Blender 2.83 and fixes a few minor issues. See the changelog for details. 0.6.10 This releases adds new Selection tools like LSelect and SSelect. Lselect can select edge loops based on an angle threshold, which allows for loop selection next to ngons. In Face mode, LSelect can select face loops based on an initial 2 face selection. This mode is specifically made to easily select perimeter loops on the outer bounds of plugs. SSelect can simplify the selection of sharp edges. Based on an initial selection of sharp edges, the tool will select all other sharp edges touching the initial selection. Again, this is helpful in cases where Blender's default loop selection fails due to the presences of ngons. Both tools can be found the Select sub menu This version also introduces the ability to edit existing stashes . Just bring up the ViewStashes tool, pick a stash and press the E key. When you are done, press ALT + ESC to exit edit stash mode. Finally, the LoopTools Circle Wrapper now has the ability to fix the midpoint, which can be way off, if the circle has an irregular vert distribution. Use the X key once the tools is running. 0.6.9 This release updates MESHmachine to properly work with recent Blender builds, which introduced some deep internal changes. As a result, Plugs can now be used with Redo Last , there are no longer deformation and rotation issues. There is however also a new issue , preventing Array plugs from being normal transferred. This should hopefully be solved over the next days without the need for another MESHmachine update. Other than that, there have been a number of smaller fixes and tweaks. The multi-region issue affecting modal tools and modal HUDs has been fixed. A rare drawing issue for the BooleanCleanup tool has been resoluved as well. The stashes HUD in the 3D View can now be scaled using Blender's ui_scale pref, as well as MESHmachine's modal_HUD_scale pref. All changes can be seen in the changelog . 0.6.8 The 0.6.8 release is the first MESHmachine for Blender 2.80. It is for the most part a straight port of MESHmachine 0.6. There are only a few additions, but numerous tweaks and improvements. Users of the previous 0.6 version should feel right at home, maybe more so than ever - now in Blender 2.80. A few things - mostly legacy modal options - have been removed to streamline the codebase going forwards. Check out the changelog for a detailed list of all changes. Attention There are two open Blender bugs affecting the Plug tool : T64300 and T64307 . Until they are fixed, there will be issues with plugging and Redo Last . This means, you currently can't change the plug rotation via the redo panel , and Plug deformation will stop working, as soon as any property is changed in the redo panel . You should be able to work around that by manualy undoing as demonstrated here . Furthermore, the contain and normal transfer options are now temporarily enabled by default to avoid unecessary undos. This comes at a ~50% performance cost. 0.6 Quite a lot has happened since 0.5.13, and even more has happened since the initial 0.5 release, 6 months ago. To get a better understanding of the amount of work that went into the 0.6 release, feel free to glance over the changelog . new since 0.5.13 First and foremost, highly anticipated, Plugs are here. Plugs are a quick way to add detail to a mesh. Make sure to check out the Plugs Introduction first. The Plug tool is the biggest, most complex tool I've worked on so far, and it has a few controls to tune its behavior, which you should learn first. The Plug tool alone is nothing, without the plug library system, which allows you easily access and manage plug assets. There are a number of example plugs to familiarize yourself with the plug tools and ideas. Beyond these example plugs, you can also get 3rd party plug libraries . What I really want, is for you to create your own plugs . And you are of course free to sell or share your plug libraries, if you want. Beyond Plugs, there are two other powerful tools called Real Mirror and VSelect. Real Mirror turns mirror modifiers into real, separate geometry with proper origins and orientation. It also mirrors custom normals. VSelect is extremely useful to select geometry based on vertex group membership. It's perfect in combination with the Normal Transfer and Conform tools. Furthermore, the modal HUDs can now be scaled. Check out the preferences for details. I have removed the HUD positioning options for now. There are a number of problems with fixed HUD positions and I didn't have the time and patience to work around them. Following the mouse position is a superior approach IMHO and it's what I use. Let me know if you hate this :) Finally, and perhaps most importantly, I have invested a lot of time into documentation, I hope it was spent well. Every single tool is documented and demonstrated in narrated videos, which you can watch via this youtube playlist . These videos are also embedded throughout this very documentation, which I think is a better way to consume them. Also, checkout the updated FAQ , where I explain some of the core ideas and theory behind MESHmachine. new since 0.5 Where should I start? Take a look at the sidebar to the left. The initial release only had the chamfer and fillet tools and had them in their earliest iterations. All of these have been improved and expanded. All of them are (optionally) modal now, making them so much more convenient to use. The concept of Stashes has been introduced. Normal tools , post-boolean tools and mirror tools have been built. The list goes on, and will keep growing. beyond 0.6 I feel like 0.6 is in a great state now. A lot of pieces are in place and I'm looking forward to spend some serious art time with this release. No doubt, there are still kinks to smooth out, and tool performances to be improved. This will be the focus of 0.7, I think. I've got plans for some new tools as well. There's also the 2.80 port to be done, which will likely happen before 0.7. Quite a few people have been very excited for this release, I just hope it doesn't disappoint. I'm certainly very happy with how MESHmachine has evolved. It really has come a long way and I've learned so much in the process. Happy plugging. 0.5 The initial release provides a number of tools, that work in tandem and outline a modeling workflow, that was previously impossible. MESHmachine should dramatically increase your flexibility, especially when working with bevels on a geometry level. It is not to be used in isolation and does not intend to replace any other tools. Rather, it's an addition to the basic modeling toolset, which IMHO really should have been ubiquitous in 3D software for at least the past decade, hence the subtitle :) This release aims to test the waters, to find out what the larger community thinks and to retrieve feedback as well as uncover edge cases where the tools fail to work. Fuse The Fuse tool is the center piece of MESHmachine. Its purpose is to create rounded surfaces from chamfers/flat bevels. Change Width Using the Change Width tool you can easily adjust the width of an existing chamfer. Unfuse Unfuse turns a curved surface created by Fuse or the Bevel and Bridge tools into a chamfer. Unchamfer The Unchamfer tool turns a chamfer back into into a hard edge. Now, with these 4 tools in place, a few others could be build, just by combining them. The Refuse tool is just the Unfuse and Fuse tools called in sequence, which effectively means you can edit bevel geometry. Similarly, the Unbevel tool is just Unfuse and Unchamfer called in sequence. In addition there's a few more tools, that can be very useful in dealing with speciic situations Unf*ck Unf*ck aligns vertices along an implicit spline curve. This is especially useful in situations were the Bevel tool overshoots vertex positions. Turn Corner The Turn Corner tool is used to re-direct the flow of a chamfer. Quad Corner The Quad Corner tool is used to convert a triangular bevel corner into a quad corner. That's about it for the inital release. I'm looking forward to feedback. There will likely be a number of edge cases, that I didn't catch yet and I hope to fix these as soon as they are reported . Looking back at how DECALmachine evolved in one year, I can't wait to see what MESHmachine will become over the next 12 months..", "title": "What's new?"}, {"location": "whatsnew.html#_1", "text": "", "title": ""}, {"location": "whatsnew.html#017", "text": "2024-11-22 Small update. Blender 4.3 does not seem to require any changes, but I've ticked a few things of my todo list and fixed two small issues with this release. LSelect (and the Select wrapper) now allow you to limit the loop selection via a step count. limit loop selections using a step count Unf*ck now supports multiple separate selections at once. multiple indepented edge loop selections adjusted in one go Fuse , Refuse and QuadCorner will now maintain the material index of the selection. material indices are now maintained for meshes with multiple materials Finally, I've added a modal wrapper for LoopTool's Space tool. LoopTools' Space now has a modal wrapper As always, check out the changelog for a list of all changes.", "title": "0.17"}, {"location": "whatsnew.html#016", "text": "2024-07-10 This release bring some minor tweaks to stashing behavior . Previously modifiers on stash objects were only applied when invoking the tool with with ALT mod key being held. Now, it's mode dependent. In Object mode modifiers will be applied, unless ALT is being held. In Edit Mesh mode modifiers are not applied, unless ALT is being held. The tooltip will reflect that accordingly, if you ever are in doubt, and the fading wire will give you a clue as well of course. The BooleanApply tool now properly supports redoing, and again you now have have control over how modifiers should be treated on the stash objects, this time from the redo panel. By default they will be applied for the stashes created from the boolean operand objects, whereas the boolean host object's stash will not have the mods applied. I think these are more sensible defaults, when working with objects that have modifiers - which was never really a focus for me when developing MESHmachine and the workflows around it. There are also a few fixes and you can see them all in the changelog . Blender 4.2 seems to work without issues, and so now everything from Blender 3.6-4.2 is supported. If you find anything causing problems, please let me know .", "title": "0.16"}, {"location": "whatsnew.html#0154", "text": "2024-05-16 Bad day.", "title": "0.15.4"}, {"location": "whatsnew.html#0153", "text": "2024-05-16 This is yet another mostly bugfix release. In the previous build I accidental<PERSON> introduced an exception when switching the Fuse tool to BRIDGE mode. The Symmetrize tool gets the option to not mirror vertex groups now, which is the new default behavior. If you dislike this, please let me know, and I can find a better solution. I chose to do this primarily due to vertex group based Bevels I use in HyperCursor. There are further improvments to the AutoSmooth mod setup in Blender 4.1, by the Boolean tool, when using the S smooth shade toggle. Also, creating stashes is now prevented for non-mesh objects, which always should have been like this. See the changelog for a list of all changes.", "title": "0.15.3"}, {"location": "whatsnew.html#0152", "text": "2024-04-10 This is a bugfix release for the most part, fixing a few issues that have come up. The newly introduced always_loop_select ability of the Select tool/wrapper, unintentionally prevented doing additional edge selections, after it had made one multi-edge selection already. The Boolean tool - when adding an Auto Smooth mod - now does it using a faster more direct method. NormalTransfer and Offset Cut , both fix rare exceptions, and when bringing a Plug into the scene, MESHmachine will now automatically remove any Auto Smooth mod, that may be present on it. Blender adds these mods automatically on all mesh objects, that were created in Blender 4 or earlier, if they had the auto smooth prop enabled. Finally, there has been some HyperCursor unreleased integration work, that ensures Geometry Gizmos are maintained when using MESHmachine's fillet tool set - so Fuse, Refuse, Unfuse, Unchamfer and Unbevel. For Fuse and Refuse, even vertex groups are maintained across each sweep's verts. Finally, the integrated updated has been updated to always expose the Re-Scan button.", "title": "0.15.2"}, {"location": "whatsnew.html#0151", "text": "2024-03-19 This was a tiny silent release, fixing a small oversight.", "title": "0.15.1"}, {"location": "whatsnew.html#015", "text": "2024-03-18 This release brings Blender 4.1 support, which introduced changes to (Auto)Smooth behavior . Beyond that, there are a couple of improvements on the Boolean , Symmetrize and Select tools. With the Boolean tool in Blender 4.1, when Auto Smooth is enabled through the modifier, either on the operand object(s), or on the active object itself, because you are toggling it in the modal using the S key, the mod will be added towards the end of the stack, but always before Mirror or Array mods, they any of these be at the end of the stack. Also operand objects are now also hidden using cycles visibility settings, not only using the hide_render prop. Symmetrize now exposes the angle threshold used for redundant center edge removal The Select wrapper has received an Always Loop Select toggle The Stashes HUD has been improved as well, and will no longer collide with MACHIN3tool's Focus HUD.", "title": "0.15"}, {"location": "whatsnew.html#get-support", "text": "The GetSupport tool has been made more accessible and is placed at the top of the addon preferences It will now - in Blender 4.1 - automatically open the readme.html with instructions in your web browser. Those same instructions can be found here in the docs of course.", "title": "Get Support"}, {"location": "whatsnew.html#integrated-updater", "text": "Finally, there is now an integrated way to do an update installation still from a .zip file! from inside of Blender. So no longer, will you have to do it from the file browser, to maintain your previous settings. It also - by default - ensures your plug assets are not lost, should you have kept them in the addon location still. Also placed at the top of the addon preferences: the new integrated updater If you have just downloaded your future MESHmachine update, the updater will find it for you! Keep in mind Since this feature is only introduced now in 0.15, it will only be of use for upcoming releases, so can't be used to install this very 0.15 update yet. In the screenshot above, old versions are used for demonstration. But of course you could downgrade with this tool as well.", "title": "Integrated Updater"}, {"location": "whatsnew.html#more", "text": "As always, check the changelog for a list of all changes.", "title": "more"}, {"location": "whatsnew.html#pricing", "text": "I've decided to bump the price a little, to counter a general downtrend in revenue, in face of rising living and so development costs, that I have observed for over a year now. The reasons for this are not clear, but it's likely a mix of multiple: post-COVID era, reaching market saturation (?), increased competition, lack of new products and social media activity, while my main focus remains on getting HyperCursor out, gen AI, inflation, layoffs, etc. For now, I still want to avoid charging for updates, but it may be inevitable. I'll see how it goes. If you are still reading, note that I have a Patreon account, and HyperCursor is in pre-release. Thanks for your support!", "title": "Pricing"}, {"location": "whatsnew.html#014", "text": "2023-11-14 This is the Blender 4.0 compatibility release. Check out the changelog for a list of all changes.", "title": "0.14"}, {"location": "whatsnew.html#013", "text": "2023-06-28 Blender 3.6 - a new LTS release is out, and it finally allows for the complete removal of the legacy bgl module. Beyond the bgl removal, there are a few smaller tweaks and improvements, such as new mapping methods for Normal Transfers, improved Symmetrize redundant center edge removal, and finally extremly reliable timer modals, which no longer suffer from occasional, unintended speed ups, which could be quite annoying for the Add Boolean tool for instance. Upon popular request, I've also added the Tutorial.blend file again, which was originally supplied with the pre-0.6 releases. You can load it from the MESHmachine help panel in the 3D view. Furthermore, the MESHmachine sidebar panel can now be completely disabled in the addon prefs. See the changelog for a list of all changes", "title": "0.13"}, {"location": "whatsnew.html#012", "text": "2022-12-30 A small feature release, it focuses mostly on playing nice alongside HyperCursor , which is in pre-release on Patreon since Christmas Eve. Consequentially the boolean tools have been updated. BooleanDuplicate has been rewritten completely and will now duplicate the entire object tree, which includes modifier objects, even those that aren't parented. It will now also no longer initiate the Translate tool at the end, which allows for the operator props to remain accessible from the redo panel. Split Booleans from the AddBoolean tool have been made more robust. The Symmetrize tool will now by default remove the center edge loop, it it's redundant, based on the edges' angles. Fuse , Change Width , etc have been fine-tuned to better work at varying object scales. See the changelog for a complete list of changes. Happy New Year!", "title": "0.12"}, {"location": "whatsnew.html#0112", "text": "2022-04-30 This release hopefully resolves some odd 3.1 related crashes. I've also added small, one-time thank you message, when opening the addon preferences forfthe first time.", "title": "0.11.2"}, {"location": "whatsnew.html#0111", "text": "2022-04-24 The previous release has been very solid. So besides a few minor tweaks and adjusments 0.11.1 only really prevents cutters created by the Boolean tool from being rendered.", "title": "0.11.1"}, {"location": "whatsnew.html#011", "text": "2022-03-09 This is the Blender 3.1 release. In addition to 3.1 support, 0.11 also provides a permanent solution to the vertex group memory issue affecting NormalTransefr , Conform and Plug , that was introduced in Blender 3.0, and for which 0.10 only provided a workaround, and one that didn't include Plug. And that's about it. Lately, I was rather occupied with PUNCHit and MACHIN3tools and even some design work for once. Oh, I also have a Patreon account now, where I will do pre-releases of upcoming addons, and where I share blend files , if I manage to find time for art. So if you are interested in further supporting my work, that would be a good way to do it. It is very much appreciated! Thank you for considering!", "title": "0.11"}, {"location": "whatsnew.html#010", "text": "2021-12-24 This is a small feature update. Both new features are based on user feedback, so thanks to you guys for suggesting these.", "title": "0.10"}, {"location": "whatsnew.html#boolean", "text": "The Duplicate Boolean tool is now fully recursive, which means, it can duplicate or instance complex boolean setups, where the boolean operands themselves have boolean modifiers. This includes BoxCutter insets. Note You still only need to have the main object selected, and all cutters can remain hidden.", "title": "Boolean"}, {"location": "whatsnew.html#symmetrize", "text": "The Symmetrize tool now has a Remove mode, which you can toggle using the X key. Instead of mirroring the mesh along one axis and in one direction, it will instead remove half the mesh in the chosen direction. Furthermore, using the S key you can now also choose to affect only the selected parts of a mesh. This works for both - Symmetrize and Remove mode.", "title": "Symmetrize"}, {"location": "whatsnew.html#more_1", "text": "In addition there are a few fixes in regards to MACHIN3tools' group empties. Check out the changelog for details. Merry Christmas, enjoy the holidays!", "title": "More"}, {"location": "whatsnew.html#091", "text": "2021-12-18 This mostly a bugfix release, but it also improves Flatten and Unchamfer. Blender 3.0 unfortunatly broke the NormalTransferand Conform tools, due a bug relating to vertex groups . Furthermore, due to a change in keymap property representation in Blender 3.0, it was no longer possible to add the Split or Delete tools (depending on your keymap choice of Y or X ) to the MESHmachine menu. This has been fixed now. Check out the changelog for details.", "title": "0.9.1"}, {"location": "whatsnew.html#09", "text": "2021-11-25 This is the Blender 3.0 release. Note, that at the time of this release, Blender 3.0 is still in beta. Alongside 3.0, Blender 2.93 LTS is still being supported, but support for any earlier versions is dropped now. This release also no longer relies on Blender's bgl module for VIEW3D drawing. However, if you want MESHmachine to draw smooth, anti-aliased lines, you need to enable Use Legacy Line Smoothing in the addon preferences , which still uses bgl .", "title": "0.9"}, {"location": "whatsnew.html#boolean_1", "text": "This release adds the Duplicate Boolean tool, which is used to easily duplicate or instance objects with one or multiple boolean modifiers, including all the boolean objects - aka \"cutters\" - even if they are hidden. The Apply Boolean tool has been updated to support applying boolean mods on multi-object selections.", "title": "Boolean"}, {"location": "whatsnew.html#unfuse-and-unbevel", "text": "Both tools will no longer set sharp edges by default, unless the face selection actually consists of smooth faces.", "title": "Unfuse and Unbevel"}, {"location": "whatsnew.html#more_2", "text": "There are various smaller tweaks and adjustments, such as improvements to the GetSupport tool and the removal of legacy code for pre-2.93 case handling. Finally note that, due to the number of tools in the addon, not everything could be fully tested in 3.0, so some issues may still arise and should be reported .", "title": "More"}, {"location": "whatsnew.html#plugs", "text": "To conclude, here is a very interesting, topology-focused use case for Plugs , which you may want to check out, by Unis on gumroad.", "title": "Plugs"}, {"location": "whatsnew.html#082", "text": "A tiny bugfix release addressing an issue on some linux systems. Also, disable Wedge debug output, which was accidentally left enabled in 0.8.1.", "title": "0.8.2"}, {"location": "whatsnew.html#081", "text": "This is a hotfix release for the Wedge tool. No other changes.", "title": "0.8.1"}, {"location": "whatsnew.html#08", "text": "MESHmachine 0.8 is the official 2.93 release, and I'd ask you to update to 2.93, if you haven't already. 2.93 is the latest stable release, and the latest LTS release at the same time. Any issues occurring only in earlier versions will not be addressed going forward. This is a feature release adding the Wedge tool , Split Booleans and related tooling, and the ability to set the Cursor to Stashes . Beyond that there have been some tweaks and a few fixes.", "title": "0.8"}, {"location": "whatsnew.html#cursor-to-stash", "text": "", "title": "Cursor to Stash"}, {"location": "whatsnew.html#split-booleans", "text": "", "title": "Split Booleans"}, {"location": "whatsnew.html#wedge", "text": "Check out the changelog for the full list of changes.", "title": "Wedge"}, {"location": "whatsnew.html#072", "text": "The second release in the 0.7 cycle resolves an issue with subset plug creation. It also adds the ability to stash evaluated meshes by holding ALT , and improves dealing with child objects when swapping stashes. See the changelog for details.", "title": "0.7.2"}, {"location": "whatsnew.html#071", "text": "This is a small bugfix release with some UI tweaks. If you have 0.7 installed already, I'd consider this an optional update, depending on whether you require any of the supplied fixes or tweaks . The easiest way to install a bugfix release like this one, is to simply overwrite the existing MESHmachine addon folder with the MESHmachine folder in the 0.7.1 zip file. There's no need to delete anything.", "title": "0.7.1"}, {"location": "whatsnew.html#07", "text": "The 0.7 release is the first real feature release since 0.6, which released 2.5 years ago. While there have been the 2.80 port and a few compatibility releases - some with new features sneaked in - there hasn't been a lot of development happening since 0.6. This is due to the complete rewrite of DECALmachine for Blender 2.80, as well as the following updates creating a full decal export pipeline , which took 2+ years in total. MESHmachine 0.7 then is the result of finally spending some new development time on MESHmachine again, and pulling together a few unreleased features I had been sitting on for those past 2+ years. It brings various improvements in regards to existing workflows, some new tools and features, and new documentation. 0.7 is only the start though, I will make MESHmachine a main focus of my development efforts for 2021, and likely 2022 as well.", "title": "0.7"}, {"location": "whatsnew.html#installation-support", "text": "Please make sure to check the updated installation instructions , as well as the preferences . Also see this , if you require product support.", "title": "Installation &amp; Support"}, {"location": "whatsnew.html#boolean_2", "text": "MESHmachine 0.7 adds two boolean tools , for adding and applying booleans. Unique about them - besides the convenience - is the integration with stashes .", "title": "Boolean"}, {"location": "whatsnew.html#selection", "text": "While VSelect was present in 0.6 already, LSelect and SSelect only came with 0.6.10 and remained undocumented - until now. Still, buried in a sub-menu, all three could be awkard to use, considering how frequent selections are done while modeling. The new Select tool rectifies this.", "title": "Selection"}, {"location": "whatsnew.html#symmetrize_1", "text": "MESHmachine's Symmetrize has been a bit restrictive in the past, as it required 3 separate keymaps to mirror in 3 directions, and at all times an awareness of how your object is aligned. If you wanted to mirror in one of the other 3 directions, you'd have to use the redo panel. Version 0.7 adds the flick mode , which means you can use a single keymap to mirror in all 6 directions, in a manner very similar to how pie menus work.", "title": "Symmetrize"}, {"location": "whatsnew.html#stashes", "text": "Stashes, a central feature of MESHmachine, are becomming even better. You can now conveniently access them from the sidebar, complementing the View Stashes tool. From here you can now even define custom names. In addition to editing and retrieving stashes, you can now also swap them with the active object. Furthermore, removing object stashes, and dealing with orphan stashes has been simplified. You can now create stashes from face selections, and you can sweep stashes if they end up cluttering your scene after appending objects.", "title": "Stashes"}, {"location": "whatsnew.html#more_3", "text": "If you ever needed to delete a plug, know that you can do that easily now . And if you ever forgot to create a stash and needed a normal source to flx a shading issue, perhaps after plugging, you can try creating a Quick Patch with MESHmachine 0.7.", "title": "More"}, {"location": "whatsnew.html#experimental-features", "text": "I have decided to release a few experimental features with this release, the most exciting being the OffsetCut tool. Please understand, that experimental features are undocumented, untested and not covered by product support. I consider them unfinished, see this for details. In conclusion, I want to thank you for your patience and for your support. You can check out the changelog for details on this release, as well as view all new videos in this playlist .", "title": "Experimental Features"}, {"location": "whatsnew.html#0613", "text": "This is a tiny bugfix release for users of 2.90, that fixes an exception when adding a plug to a library.", "title": "0.6.13"}, {"location": "whatsnew.html#0612", "text": "This release ensures compatibility with Blender 2.90 and adds ALT navigation support for users of the Industry Compatibly keymap. It also changes how panels are registered to support proper workspace filtering.", "title": "0.6.12"}, {"location": "whatsnew.html#0611", "text": "This release ensures compatibility with Blender 2.83 and fixes a few minor issues. See the changelog for details.", "title": "0.6.11"}, {"location": "whatsnew.html#0610", "text": "This releases adds new Selection tools like LSelect and SSelect. Lselect can select edge loops based on an angle threshold, which allows for loop selection next to ngons. In Face mode, LSelect can select face loops based on an initial 2 face selection. This mode is specifically made to easily select perimeter loops on the outer bounds of plugs. SSelect can simplify the selection of sharp edges. Based on an initial selection of sharp edges, the tool will select all other sharp edges touching the initial selection. Again, this is helpful in cases where Blender's default loop selection fails due to the presences of ngons. Both tools can be found the Select sub menu This version also introduces the ability to edit existing stashes . Just bring up the ViewStashes tool, pick a stash and press the E key. When you are done, press ALT + ESC to exit edit stash mode. Finally, the LoopTools Circle Wrapper now has the ability to fix the midpoint, which can be way off, if the circle has an irregular vert distribution. Use the X key once the tools is running.", "title": "0.6.10"}, {"location": "whatsnew.html#069", "text": "This release updates MESHmachine to properly work with recent Blender builds, which introduced some deep internal changes. As a result, Plugs can now be used with Redo Last , there are no longer deformation and rotation issues. There is however also a new issue , preventing Array plugs from being normal transferred. This should hopefully be solved over the next days without the need for another MESHmachine update. Other than that, there have been a number of smaller fixes and tweaks. The multi-region issue affecting modal tools and modal HUDs has been fixed. A rare drawing issue for the BooleanCleanup tool has been resoluved as well. The stashes HUD in the 3D View can now be scaled using Blender's ui_scale pref, as well as MESHmachine's modal_HUD_scale pref. All changes can be seen in the changelog .", "title": "0.6.9"}, {"location": "whatsnew.html#068", "text": "The 0.6.8 release is the first MESHmachine for Blender 2.80. It is for the most part a straight port of MESHmachine 0.6. There are only a few additions, but numerous tweaks and improvements. Users of the previous 0.6 version should feel right at home, maybe more so than ever - now in Blender 2.80. A few things - mostly legacy modal options - have been removed to streamline the codebase going forwards. Check out the changelog for a detailed list of all changes. Attention There are two open Blender bugs affecting the Plug tool : T64300 and T64307 . Until they are fixed, there will be issues with plugging and Redo Last . This means, you currently can't change the plug rotation via the redo panel , and Plug deformation will stop working, as soon as any property is changed in the redo panel . You should be able to work around that by manualy undoing as demonstrated here . Furthermore, the contain and normal transfer options are now temporarily enabled by default to avoid unecessary undos. This comes at a ~50% performance cost.", "title": "0.6.8"}, {"location": "whatsnew.html#06", "text": "Quite a lot has happened since 0.5.13, and even more has happened since the initial 0.5 release, 6 months ago. To get a better understanding of the amount of work that went into the 0.6 release, feel free to glance over the changelog .", "title": "0.6"}, {"location": "whatsnew.html#new-since-0513", "text": "First and foremost, highly anticipated, Plugs are here. Plugs are a quick way to add detail to a mesh. Make sure to check out the Plugs Introduction first. The Plug tool is the biggest, most complex tool I've worked on so far, and it has a few controls to tune its behavior, which you should learn first. The Plug tool alone is nothing, without the plug library system, which allows you easily access and manage plug assets. There are a number of example plugs to familiarize yourself with the plug tools and ideas. Beyond these example plugs, you can also get 3rd party plug libraries . What I really want, is for you to create your own plugs . And you are of course free to sell or share your plug libraries, if you want. Beyond Plugs, there are two other powerful tools called Real Mirror and VSelect. Real Mirror turns mirror modifiers into real, separate geometry with proper origins and orientation. It also mirrors custom normals. VSelect is extremely useful to select geometry based on vertex group membership. It's perfect in combination with the Normal Transfer and Conform tools. Furthermore, the modal HUDs can now be scaled. Check out the preferences for details. I have removed the HUD positioning options for now. There are a number of problems with fixed HUD positions and I didn't have the time and patience to work around them. Following the mouse position is a superior approach IMHO and it's what I use. Let me know if you hate this :) Finally, and perhaps most importantly, I have invested a lot of time into documentation, I hope it was spent well. Every single tool is documented and demonstrated in narrated videos, which you can watch via this youtube playlist . These videos are also embedded throughout this very documentation, which I think is a better way to consume them. Also, checkout the updated FAQ , where I explain some of the core ideas and theory behind MESHmachine.", "title": "new since 0.5.13"}, {"location": "whatsnew.html#new-since-05", "text": "Where should I start? Take a look at the sidebar to the left. The initial release only had the chamfer and fillet tools and had them in their earliest iterations. All of these have been improved and expanded. All of them are (optionally) modal now, making them so much more convenient to use. The concept of Stashes has been introduced. Normal tools , post-boolean tools and mirror tools have been built. The list goes on, and will keep growing.", "title": "new since 0.5"}, {"location": "whatsnew.html#beyond-06", "text": "I feel like 0.6 is in a great state now. A lot of pieces are in place and I'm looking forward to spend some serious art time with this release. No doubt, there are still kinks to smooth out, and tool performances to be improved. This will be the focus of 0.7, I think. I've got plans for some new tools as well. There's also the 2.80 port to be done, which will likely happen before 0.7. Quite a few people have been very excited for this release, I just hope it doesn't disappoint. I'm certainly very happy with how MESHmachine has evolved. It really has come a long way and I've learned so much in the process. Happy plugging.", "title": "beyond 0.6"}, {"location": "whatsnew.html#05", "text": "The initial release provides a number of tools, that work in tandem and outline a modeling workflow, that was previously impossible. MESHmachine should dramatically increase your flexibility, especially when working with bevels on a geometry level. It is not to be used in isolation and does not intend to replace any other tools. Rather, it's an addition to the basic modeling toolset, which IMHO really should have been ubiquitous in 3D software for at least the past decade, hence the subtitle :) This release aims to test the waters, to find out what the larger community thinks and to retrieve feedback as well as uncover edge cases where the tools fail to work.", "title": "0.5"}, {"location": "whatsnew.html#fuse", "text": "The Fuse tool is the center piece of MESHmachine. Its purpose is to create rounded surfaces from chamfers/flat bevels.", "title": "<PERSON><PERSON>"}, {"location": "whatsnew.html#change-width", "text": "Using the Change Width tool you can easily adjust the width of an existing chamfer.", "title": "Change Width"}, {"location": "whatsnew.html#unfuse", "text": "Unfuse turns a curved surface created by <PERSON><PERSON> or the Bevel and Bridge tools into a chamfer.", "title": "Unfuse"}, {"location": "whatsnew.html#unchamfer", "text": "The Unchamfer tool turns a chamfer back into into a hard edge. Now, with these 4 tools in place, a few others could be build, just by combining them. The Refuse tool is just the Unfuse and Fuse tools called in sequence, which effectively means you can edit bevel geometry. Similarly, the Unbevel tool is just Unfuse and <PERSON>cha<PERSON><PERSON> called in sequence. In addition there's a few more tools, that can be very useful in dealing with speciic situations", "title": "<PERSON><PERSON><PERSON><PERSON>"}, {"location": "whatsnew.html#unfck", "text": "Unf*ck aligns vertices along an implicit spline curve. This is especially useful in situations were the Bevel tool overshoots vertex positions.", "title": "Unf*ck"}, {"location": "whatsnew.html#turn-corner", "text": "The Turn Corner tool is used to re-direct the flow of a chamfer.", "title": "Turn Corner"}, {"location": "whatsnew.html#quad-corner", "text": "The Quad Corner tool is used to convert a triangular bevel corner into a quad corner. That's about it for the inital release. I'm looking forward to feedback. There will likely be a number of edge cases, that I didn't catch yet and I hope to fix these as soon as they are reported . Looking back at how DECALmachine evolved in one year, I can't wait to see what MESHmachine will become over the next 12 months..", "title": "Quad Corner"}]}