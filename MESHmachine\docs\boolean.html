<!DOCTYPE html>
<html class="writer-html5" lang="en" >
<head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" /><link rel="canonical" href="https://machin3.io/MESHmachine/docs/boolean.html" />
      <link rel="shortcut icon" href="img/favicon.ico" />
    <title>Boolean - MESHmachine</title>
    <link rel="stylesheet" href="css/theme.css" />
    <link rel="stylesheet" href="css/theme_extra.css" />
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.5.0/styles/github.min.css" />
        <link href="css/extra.css" rel="stylesheet" />
    
      <script>
        // Current page data
        var mkdocs_page_name = "Boolean";
        var mkdocs_page_input_path = "boolean.md";
        var mkdocs_page_url = "/MESHmachine/docs/boolean.html";
      </script>
    
    <script src="js/jquery-3.6.0.min.js" defer></script>
    <!--[if lt IE 9]>
      <script src="js/html5shiv.min.js"></script>
    <![endif]-->
      <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.5.0/highlight.min.js"></script>
      <script>hljs.initHighlightingOnLoad();</script> 
</head>

<body class="wy-body-for-nav" role="document">

  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side stickynav">
    <div class="wy-side-scroll">
      <div class="wy-side-nav-search">
          <a href="." class="icon icon-home"> MESHmachine
        </a><div role="search">
  <form id ="rtd-search-form" class="wy-form" action="./search.html" method="get">
      <input type="text" name="q" placeholder="Search docs" title="Type search term here" />
  </form>
</div>
      </div>

      <div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="Navigation menu">
              <p class="caption"><span class="caption-text">Get Started</span></p>
              <ul>
                  <li class="toctree-l1"><a class="reference internal" href="index.html">MESHmachine</a>
                  </li>
                  <li class="toctree-l1"><a class="reference internal" href="installation.html">Installation</a>
                  </li>
                  <li class="toctree-l1"><a class="reference internal" href="preferences.html">Preferences</a>
                  </li>
                  <li class="toctree-l1"><a class="reference internal" href="whatsnew.html">What's new?</a>
                  </li>
                  <li class="toctree-l1"><a class="reference internal" href="faq.html">FAQ</a>
                  </li>
              </ul>
              <p class="caption"><span class="caption-text">Chamfers and Fillets</span></p>
              <ul>
                  <li class="toctree-l1"><a class="reference internal" href="change_width.html">Change Width</a>
                  </li>
                  <li class="toctree-l1"><a class="reference internal" href="fuse.html">Fuse</a>
                  </li>
                  <li class="toctree-l1"><a class="reference internal" href="flatten.html">Flatten</a>
                  </li>
                  <li class="toctree-l1"><a class="reference internal" href="unfuse.html">Unfuse</a>
                  </li>
                  <li class="toctree-l1"><a class="reference internal" href="refuse.html">Refuse</a>
                  </li>
                  <li class="toctree-l1"><a class="reference internal" href="unchamfer.html">Unchamfer</a>
                  </li>
                  <li class="toctree-l1"><a class="reference internal" href="unbevel.html">Unbevel</a>
                  </li>
                  <li class="toctree-l1"><a class="reference internal" href="unfuck.html">Unf*ck</a>
                  </li>
                  <li class="toctree-l1"><a class="reference internal" href="turn_corner.html">Turn Corner</a>
                  </li>
                  <li class="toctree-l1"><a class="reference internal" href="quad_corner.html">Quad Corner</a>
                  </li>
                  <li class="toctree-l1"><a class="reference internal" href="mark_loop.html">Mark Loop</a>
                  </li>
              </ul>
              <p class="caption"><span class="caption-text">Plugs</span></p>
              <ul>
                  <li class="toctree-l1"><a class="reference internal" href="plugs_introduction.html">Plugs Introduction</a>
                  </li>
                  <li class="toctree-l1"><a class="reference internal" href="plug_creation.html">Plug Creation</a>
                  </li>
                  <li class="toctree-l1"><a class="reference internal" href="advanced_plug_creation.html">Advanced Plug Creation</a>
                  </li>
                  <li class="toctree-l1"><a class="reference internal" href="remove_plugs.html">Remove Plugs</a>
                  </li>
                  <li class="toctree-l1"><a class="reference internal" href="delete_plug.html">Delete Plug</a>
                  </li>
                  <li class="toctree-l1"><a class="reference internal" href="plug_resources.html">Plug Resources</a>
                  </li>
              </ul>
              <p class="caption"><span class="caption-text">Booleans</span></p>
              <ul class="current">
                  <li class="toctree-l1 current"><a class="reference internal current" href="boolean.html">Boolean</a>
    <ul class="current">
    <li class="toctree-l2"><a class="reference internal" href="#add-apply-boolean">Add &amp; Apply Boolean</a>
        <ul>
    <li class="toctree-l3"><a class="reference internal" href="#split-mode">Split Mode</a>
    </li>
    <li class="toctree-l3"><a class="reference internal" href="#selection">Selection</a>
    </li>
    <li class="toctree-l3"><a class="reference internal" href="#using-add-apply-boolean">Using Add &amp; Apply Boolean</a>
    </li>
        </ul>
    </li>
    <li class="toctree-l2"><a class="reference internal" href="#duplicate">Duplicate</a>
        <ul>
    <li class="toctree-l3"><a class="reference internal" href="#modes">Modes</a>
    </li>
    <li class="toctree-l3"><a class="reference internal" href="#selection_1">Selection</a>
    </li>
        </ul>
    </li>
    <li class="toctree-l2"><a class="reference internal" href="#make-unique">Make Unique</a>
        <ul>
    <li class="toctree-l3"><a class="reference internal" href="#selection_2">Selection</a>
    </li>
        </ul>
    </li>
    </ul>
                  </li>
                  <li class="toctree-l1"><a class="reference internal" href="boolean_cleanup.html">Boolean Cleanup</a>
                  </li>
                  <li class="toctree-l1"><a class="reference internal" href="chamfer.html">Chamfer</a>
                  </li>
                  <li class="toctree-l1"><a class="reference internal" href="offset.html">Offset</a>
                  </li>
              </ul>
              <p class="caption"><span class="caption-text">Stashes</span></p>
              <ul>
                  <li class="toctree-l1"><a class="reference internal" href="create_stash.html">Create Stash</a>
                  </li>
                  <li class="toctree-l1"><a class="reference internal" href="view_stashes.html">View Stashes</a>
                  </li>
                  <li class="toctree-l1"><a class="reference internal" href="transfer_stashes.html">Transfer Stashes</a>
                  </li>
                  <li class="toctree-l1"><a class="reference internal" href="stashes_panel.html">Stashes Panel</a>
                  </li>
                  <li class="toctree-l1"><a class="reference internal" href="orphan_stashes.html">Orphan Stashes</a>
                  </li>
                  <li class="toctree-l1"><a class="reference internal" href="sweep_stashes.html">Sweep Stashes</a>
                  </li>
                  <li class="toctree-l1"><a class="reference internal" href="conform.html">Conform</a>
                  </li>
                  <li class="toctree-l1"><a class="reference internal" href="quick_patch.html">Quick Patch</a>
                  </li>
              </ul>
              <p class="caption"><span class="caption-text">Normals</span></p>
              <ul>
                  <li class="toctree-l1"><a class="reference internal" href="normal_flatten_straighten.html">Normal Flatten and Straighten</a>
                  </li>
                  <li class="toctree-l1"><a class="reference internal" href="normal_transfer.html">Normal Transfer</a>
                  </li>
                  <li class="toctree-l1"><a class="reference internal" href="normal_clear.html">Normal Clear</a>
                  </li>
              </ul>
              <p class="caption"><span class="caption-text">Mirror</span></p>
              <ul>
                  <li class="toctree-l1"><a class="reference internal" href="symmetrize.html">Symmetrize</a>
                  </li>
                  <li class="toctree-l1"><a class="reference internal" href="real_mirror.html">Real Mirror</a>
                  </li>
              </ul>
              <p class="caption"><span class="caption-text">Selection</span></p>
              <ul>
                  <li class="toctree-l1"><a class="reference internal" href="lselect.html">LSelect</a>
                  </li>
                  <li class="toctree-l1"><a class="reference internal" href="sselect.html">SSelect</a>
                  </li>
                  <li class="toctree-l1"><a class="reference internal" href="vselect.html">VSelect</a>
                  </li>
                  <li class="toctree-l1"><a class="reference internal" href="select.html">Select</a>
                  </li>
              </ul>
              <p class="caption"><span class="caption-text">Refinement</span></p>
              <ul>
                  <li class="toctree-l1"><a class="reference internal" href="wedge.html">Wedge</a>
                  </li>
              </ul>
              <p class="caption"><span class="caption-text">Looptools</span></p>
              <ul>
                  <li class="toctree-l1"><a class="reference internal" href="circle_relax.html">Circle and Relax</a>
                  </li>
              </ul>
              <ul>
                <li class="toctree-l1"><a class="reference internal" href="changelog.html">Changelog</a>
                </li>
              </ul>
      </div>
    </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap">
      <nav class="wy-nav-top" role="navigation" aria-label="Mobile navigation menu">
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href=".">MESHmachine</a>
        
      </nav>
      <div class="wy-nav-content">
        <div class="rst-content"><div role="navigation" aria-label="breadcrumbs navigation">
  <ul class="wy-breadcrumbs">
    <li><a href="." class="icon icon-home" alt="Docs"></a> &raquo;</li>
          <li>Booleans &raquo;</li>
      <li>Boolean</li>
    <li class="wy-breadcrumbs-aside">
    </li>
  </ul>
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
            <div class="section" itemprop="articleBody">
              
                <h1 id="_1"></h1>
<h2 id="add-apply-boolean">Add &amp; Apply Boolean</h2>
<p><strong>shortcut</strong> <code>ya</code> <em>object mode</em></p>
<p>MESHmachine provides a quick way to add boolean modifiers, as well as apply them.<br />
If you apply boolean modifers this way, MESHmachine will automatically create <a href="create_stash.html">stashes</a> from operant objects.</p>
<h3 id="split-mode">Split Mode</h3>
<p>Version 0.8 of MESHmachine introduced the <em>Split</em> Boolean mode, which in addition to setting up the boolean modifiers, also creates a duplicate of the active object, to create the second, split-away part. Splitting will leave the object meshes, as well as the cutter meshes as instances.<br />
This means it's easy to adjust the active object's mesh or the cutter after a split was performed, and the changes will automatically carry through to the other part.</p>
<p>To get rid of the instances, you can use the new <a href="#make-unique">Make Unique</a> tool.<br />
Boolean Apply will also correctly deal with instanced splt parts.</p>
<h3 id="selection">Selection</h3>
<p>At least 2 objects, whith one being active, when <em>adding</em> a boolean modifier<br />
One or multiple objects with existing boolean modifiers when <em>applying</em> the modifiers</p>
<h3 id="using-add-apply-boolean">Using Add &amp; Apply Boolean</h3>
<!--![GifBlank](img/gif/blank.gif)-->

<div style="position: relative; width: 100%; height: 0; padding-bottom: 56.25%;">
<iframe src="https://www.youtube.com/embed/_Vt_5enDIWA" style="position: absolute; top: 0; left: 0; width: 100%; height: 100%;" allowfullscreen seamless frameBorder="0"></iframe>
</div>

<hr />
<div style="position: relative; width: 100%; height: 0; padding-bottom: 56.25%;">
<iframe src="https://www.youtube.com/embed/dAJoLS7Stz0" style="position: absolute; top: 0; left: 0; width: 100%; height: 100%;" allowfullscreen seamless frameBorder="0"></iframe>
</div>

<hr />
<h2 id="duplicate">Duplicate</h2>
<p><strong>shortcut</strong> <code>yd</code> <em>object mode</em></p>
<p><em>Duplicate Boolean</em> is used to duplicate or instance objects using booleans.<br />
As of version 0.10 this is fully recursive, meaning it will work correctly even if the operand objects aka <em>cutters</em> themselves use boolen modifiers.<br />
The tool is especially useful if <em>cutters</em> - are hidden, as you still only need to have the main object selected.</p>
<h3 id="modes">Modes</h3>
<p>Default: Duplicate the selected objects and all its cutters<br />
<code>ALT</code>: Create instances instead of duplicates</p>
<h3 id="selection_1">Selection</h3>
<p>At least 1 object with a boolean modifier</p>
<h2 id="make-unique">Make Unique</h2>
<p><strong>shortcut</strong> <code>ym</code> <em>object mode</em></p>
<p><em>Make Unique</em> is a tool specifically created to deal with <em>Split</em> Booleans, or rather the instanced meshes they create.<br />
It can also be used for instanced booleans created by the Duplicate Boolean tool.</p>
<h3 id="selection_2">Selection</h3>
<p>Any number of objects using an instanced mesh</p>
              
            </div>
          </div><footer>
    <div class="rst-footer-buttons" role="navigation" aria-label="Footer Navigation">
        <a href="plug_resources.html" class="btn btn-neutral float-left" title="Plug Resources"><span class="icon icon-circle-arrow-left"></span> Previous</a>
        <a href="boolean_cleanup.html" class="btn btn-neutral float-right" title="Boolean Cleanup">Next <span class="icon icon-circle-arrow-right"></span></a>
    </div>

  <hr/>

  <div role="contentinfo">
    <!-- Copyright etc -->
  </div>

  Built with <a href="https://www.mkdocs.org/">MkDocs</a> using a <a href="https://github.com/readthedocs/sphinx_rtd_theme">theme</a> provided by <a href="https://readthedocs.org">Read the Docs</a>.
</footer>
          
        </div>
      </div>

    </section>

  </div>

  <div class="rst-versions" role="note" aria-label="Versions">
  <span class="rst-current-version" data-toggle="rst-current-version">
    
    
      <span><a href="plug_resources.html" style="color: #fcfcfc">&laquo; Previous</a></span>
    
    
      <span><a href="boolean_cleanup.html" style="color: #fcfcfc">Next &raquo;</a></span>
    
  </span>
</div>
    <script>var base_url = '.';</script>
    <script src="js/theme_extra.js" defer></script>
    <script src="js/theme.js" defer></script>
      <script src="search/main.js" defer></script>
    <script defer>
        window.onload = function () {
            SphinxRtdTheme.Navigation.enable(true);
        };
    </script>

</body>
</html>
